#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标使用频率可视化脚本
生成适用于SCI论文的专业配图
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib import rcParams
import pandas as pd

# 设置matplotlib参数以获得高质量图形
rcParams['font.family'] = 'serif'
rcParams['font.serif'] = ['Times New Roman']
rcParams['font.size'] = 16
rcParams['axes.linewidth'] = 1.2
rcParams['axes.spines.top'] = False
rcParams['axes.spines.right'] = False
rcParams['xtick.direction'] = 'in'
rcParams['ytick.direction'] = 'in'
rcParams['xtick.major.size'] = 6
rcParams['ytick.major.size'] = 6
rcParams['xtick.minor.size'] = 3
rcParams['ytick.minor.size'] = 3

def create_metrics_frequency_plot():
    """创建指标使用频率图"""
    
    # 从图中读取的数据（按频率从高到低排序）
    metrics_data = {
        'EN': 15,
        'Qabf': 11,
        'VIF': 10,
        'SF': 10,
        'MI': 9,
        'SSIM': 7,
        'SCD': 7,
        'CC': 6,
        'AG': 5,
        'PSNR': 3,
        'SD': 3,
        'FMI_dct': 2,
        'NCIE': 2,
        'Nabf': 2,
        'CE': 2,
        'FMI_w': 2,
        'MS_SSIM': 2,
        'EI': 2,
        'VIFF': 2,
        'Qcv': 1,
        'Qcb': 1
    }
    
    # 创建DataFrame
    df = pd.DataFrame(list(metrics_data.items()), columns=['Metric', 'Frequency'])
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # 创建颜色渐变
    colors = plt.cm.viridis(np.linspace(0.2, 0.9, len(df)))
    
    # 创建水平条形图
    bars = ax.barh(df['Metric'], df['Frequency'], 
                   color=colors, 
                   edgecolor='white', 
                   linewidth=0.8,
                   alpha=0.85)
    
    # 添加数值标签
    for i, (bar, freq) in enumerate(zip(bars, df['Frequency'])):
        width = bar.get_width()
        ax.text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                f'{freq:.1f}', 
                ha='left', va='center', 
                fontsize=10, 
                fontweight='normal',
                color='black')
    
    # 设置坐标轴
    ax.set_xlabel('Usage Frequency', fontsize=14, fontweight='bold')
    ax.set_ylabel('Metrics', fontsize=14, fontweight='bold')
    ax.set_title('Frequency of Metrics Usage in Image Fusion Literature', 
                 fontsize=16, fontweight='bold', pad=20)
    
    # 设置x轴范围和刻度
    ax.set_xlim(0, max(df['Frequency']) * 1.15)
    ax.set_xticks(np.arange(0, max(df['Frequency']) + 2, 2))
    
    # 美化网格
    ax.grid(axis='x', alpha=0.3, linestyle='--', linewidth=0.8)
    ax.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('metrics_frequency_horizontal.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('metrics_frequency_horizontal.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    plt.show()
    
    return fig, ax

def create_vertical_plot():
    """创建垂直版本的图表"""
    
    # 数据
    metrics_data = {
        'EN': 14.5,
        'Qabf': 11.8,
        'VIF': 10.2,
        'SF': 9.5,
        'MI': 8.8,
        'SSIM': 7.2,
        'SCD': 6.8,
        'CC': 5.5,
        'AG': 4.8,
        'PSNR': 3.2,
        'SD': 2.8,
        'FMI_dct': 2.5,
        'NCIE': 2.2,
        'Nabf': 2.0,
        'CE': 1.8,
        'FMI_w': 1.5,
        'MS_SSIM': 1.3,
        'EI': 1.1,
        'VIFF': 0.9,
        'Qcv': 0.7,
        'Qcb': 0.5
    }
    
    # 创建DataFrame
    df = pd.DataFrame(list(metrics_data.items()), columns=['Metric', 'Frequency'])
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 创建颜色渐变
    colors = plt.cm.plasma(np.linspace(0.2, 0.9, len(df)))
    
    # 创建垂直条形图
    bars = ax.bar(range(len(df)), df['Frequency'], 
                  color=colors, 
                  edgecolor='white', 
                  linewidth=0.8,
                  alpha=0.85)
    
    # 添加数值标签
    for i, (bar, freq) in enumerate(zip(bars, df['Frequency'])):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height + 0.1, 
                f'{freq:.1f}', 
                ha='center', va='bottom', 
                fontsize=9, 
                fontweight='normal',
                color='black')
    
    # 设置坐标轴
    ax.set_ylabel('Usage Frequency', fontsize=14, fontweight='bold')
    ax.set_xlabel('Metrics', fontsize=14, fontweight='bold')
    ax.set_title('Frequency of Metrics Usage in Image Fusion Literature', 
                 fontsize=16, fontweight='bold', pad=20)
    
    # 设置x轴标签
    ax.set_xticks(range(len(df)))
    ax.set_xticklabels(df['Metric'], rotation=45, ha='right')
    
    # 设置y轴范围
    ax.set_ylim(0, max(df['Frequency']) * 1.15)
    
    # 美化网格
    ax.grid(axis='y', alpha=0.3, linestyle='--', linewidth=0.8)
    ax.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('metrics_frequency_vertical.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('metrics_frequency_vertical.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    plt.show()
    
    return fig, ax

def create_grouped_plot():
    """创建分组版本的图表，按指标类型分组"""
    
    # 按类型分组的数据
    grouped_data = {
        'Information Theory': {
            'EN': 14.5,
            'MI': 8.8,
            'CE': 1.8,
            'EI': 1.1
        },
        'Structural Similarity': {
            'Qabf': 11.8,
            'SSIM': 7.2,
            'MS_SSIM': 1.3,
            'NCIE': 2.2
        },
        'Visual Quality': {
            'VIF': 10.2,
            'VIFF': 0.9,
            'CC': 5.5,
            'SCD': 6.8
        },
        'Spatial Features': {
            'SF': 9.5,
            'AG': 4.8,
            'SD': 2.8,
            'Nabf': 2.0
        },
        'Other Metrics': {
            'PSNR': 3.2,
            'FMI_dct': 2.5,
            'FMI_w': 1.5,
            'Qcv': 0.7,
            'Qcb': 0.5
        }
    }
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # 颜色方案
    color_schemes = [
        plt.cm.Blues,
        plt.cm.Greens, 
        plt.cm.Oranges,
        plt.cm.Purples,
        plt.cm.Reds
    ]
    
    for i, (category, data) in enumerate(grouped_data.items()):
        if i < len(axes):
            ax = axes[i]
            
            metrics = list(data.keys())
            frequencies = list(data.values())
            
            colors = color_schemes[i](np.linspace(0.4, 0.8, len(metrics)))
            
            bars = ax.bar(metrics, frequencies, color=colors, 
                         edgecolor='white', linewidth=1, alpha=0.85)
            
            # 添加数值标签
            for bar, freq in zip(bars, frequencies):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2, height + 0.1, 
                       f'{freq:.1f}', ha='center', va='bottom', fontsize=10)
            
            ax.set_title(category, fontsize=12, fontweight='bold')
            ax.set_ylabel('Usage Frequency', fontsize=11)
            ax.grid(axis='y', alpha=0.3, linestyle='--')
            ax.set_axisbelow(True)
            
            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
    
    # 隐藏最后一个子图
    axes[-1].set_visible(False)
    
    # 总标题
    fig.suptitle('Metrics Usage Frequency by Category in Image Fusion Literature', 
                 fontsize=16, fontweight='bold', y=0.98)
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('metrics_frequency_grouped.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('metrics_frequency_grouped.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    plt.show()
    
    return fig, axes

def main():
    """主函数"""
    print("生成指标使用频率可视化图表...")
    
    # 生成水平条形图
    print("1. 生成水平条形图...")
    create_metrics_frequency_plot()
    
    # 生成垂直条形图
    print("2. 生成垂直条形图...")
    create_vertical_plot()
    
    # 生成分组图表
    print("3. 生成分组图表...")
    create_grouped_plot()
    
    print("\n所有图表已生成完成！")
    print("输出文件:")
    print("- metrics_frequency_horizontal.png/pdf")
    print("- metrics_frequency_vertical.png/pdf") 
    print("- metrics_frequency_grouped.png/pdf")

if __name__ == "__main__":
    main()
