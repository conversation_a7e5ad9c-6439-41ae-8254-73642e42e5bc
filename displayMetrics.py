import torch
import torchvision.transforms.functional as TF
from PIL import Image
import os
from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric, ei_metric

# 读取图像并转化为灰度图像
def read_image(path):
    image = TF.to_tensor(Image.open(path)).unsqueeze(0)
    if len(image.shape) == 3:
        image = image.mean(dim=1, keepdim=True)
    return image.cuda()

# 计算指标
def calculate_metrics(vis, ir, fused):
    metrics = {}
    metrics['CE'] = ce_metric(vis, ir, fused).item()
    metrics['EN'] = en_metric(vis, ir, fused).item()
    metrics['MI'] = mi_metric(vis, ir, fused).item()
    metrics['PSNR'] = psnr_metric(vis, ir, fused).item()
    metrics['Qabf'] = q_abf_metric(vis, ir, fused).item()
    metrics['SD'] = sd_metric(vis, ir, fused).item()
    metrics['SF'] = sf_metric(vis, ir, fused).item()
    metrics['SSIM'] = ssim_metric(vis, ir, fused).item()
    metrics['VIF'] = vif_metric(vis, ir, fused).item()
    metrics['SCD'] = scd_metric(vis, ir, fused).item()
    metrics['CC'] = cc_metric(vis, ir, fused).item()
    metrics['EI'] = ei_metric(vis, ir, fused).item()
    return metrics

# 计算指标涨幅
def calculate_improvement(metric_before, metric_after, metric_name):
    if metric_name in ['CE']:
        # 对于越小越好的指标，分降低则为正
        return -((metric_after - metric_before) / metric_before) * 100
    else:
        if metric_before == 0:
            return 0
        # 对于越大越好的指标，分升高则为正
        return ((metric_after - metric_before) / metric_before) * 100

# 打印指标
def display_metrics(vis_path, ir_path, fused_path, optimized_fused, current_iteration):
    vis = read_image(vis_path)
    ir = read_image(ir_path)
    fused = read_image(fused_path)

    metrics_fused = calculate_metrics(vis, ir, fused)
    metrics_optimized = calculate_metrics(vis, ir, optimized_fused)

    improvements = {}
    for metric_name in metrics_fused.keys(): 
        improvements[metric_name] = calculate_improvement(metrics_fused[metric_name], metrics_optimized[metric_name], metric_name)  # 修正此处

    # print(f"Iteration: {current_iteration}")
    # print("原始融合图指标:")
    # for metric_name, metric_value in metrics_fused.items():
    #     print(f"{metric_name}: {metric_value}")

    # print("\n优化后融合图指标:")
    # for metric_name, metric_value in metrics_optimized.items():
    #     print(f"{metric_name}: {metric_value}")

    print("涨幅百分比:")
    for metric_name, improvement in improvements.items():
        print(f"{metric_name}: {improvement:.2f} %")   

    

    return metrics_optimized

if __name__ == "__main__":
    vis_path = 'input/VIS/jeep.bmp'
    ir_path = 'input/IR/jeep.bmp'
    fused_path = 'input/Fused/jeep_FusionGAN.png'
    optimized_fused = torch.rand(1, 1, 512, 640)  # 这里随机生成一个张量作为示例
    display_metrics(vis_path, ir_path, fused_path, optimized_fused, 1)
