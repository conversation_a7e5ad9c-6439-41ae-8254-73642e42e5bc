import torch
import torchvision.transforms.functional as TF
from PIL import Image
import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from printMetrics4inspection import read_image, calculate_metrics, calculate_improvement, metrics_functions


fusion_method = "Baysian"  


ir_path = "F:\\.Infrared and Visiable Light Fusion\\Optimization based\\input4table\\IR"
vis_path = "F:\\.Infrared and Visiable Light Fusion\\Optimization based\\input4table\\VIS"
fused_path = f"F:\\.Infrared and Visiable Light Fusion\\Optimization based\\input4table\\Fused\\{fusion_method}"
optimized_path = f"F:\\.Infrared and Visiable Light Fusion\\Optimization based\\output\\{fusion_method}"
inspection_path = os.path.join("F:\\.Infrared and Visiable Light Fusion\\Optimization based\\output\\inspection", fusion_method)


os.makedirs(inspection_path, exist_ok=True)


image_files = sorted([f for f in os.listdir(ir_path) if os.path.isfile(os.path.join(ir_path, f))], key=lambda x: int(x.split('.')[0]))


metrics_improvements = []


for i in range(0, len(image_files), 5):
    n_imgs = min(5, len(image_files) - i)  
    fig, axs = plt.subplots(5, n_imgs, figsize=(n_imgs*4, 20))  


    modalities = ['IR', 'Vis', 'Fused_ori', 'Fused_opt']
    for row, modality in enumerate(modalities):
        axs[row, 0].text(-0.1, 0.5, modality, transform=axs[row, 0].transAxes, ha="right", va="center", fontsize=12)

    for col, img_name in enumerate(image_files[i:i+n_imgs]):

        # plt.figtext(0.2 + col*0.16, 0.97, img_name, ha='center', va='center', fontsize=12)
        axs[0, col].text(0.5, 1.05, img_name, ha='center', va='center', fontsize=16, transform=axs[0, col].transAxes)

        ir_img = read_image(os.path.join(ir_path, img_name))
        vis_img = read_image(os.path.join(vis_path, img_name))
        fused_img_name = img_name.split('.')[0] + f"_{fusion_method}.png"
        fused_img = read_image(os.path.join(fused_path, fused_img_name))
        optimized_img_name = img_name.split('.')[0] + f"_{fusion_method}_opt.png"
        optimized_img = read_image(os.path.join(optimized_path, optimized_img_name))


        axs[0, col].imshow(ir_img.squeeze().cpu().numpy(), cmap='gray')
        axs[1, col].imshow(vis_img.squeeze().cpu().numpy(), cmap='gray')
        axs[2, col].imshow(fused_img.squeeze().cpu().numpy(), cmap='gray')
        axs[3, col].imshow(optimized_img.squeeze().cpu().numpy(), cmap='gray')


        metrics_fused = calculate_metrics(vis_img, ir_img, fused_img)
        metrics_optimized = calculate_metrics(vis_img, ir_img, optimized_img)
        improvements = {}
        for metric_name in metrics_fused.keys():
            improvements[metric_name] = calculate_improvement(metrics_fused[metric_name], metrics_optimized[metric_name], metric_name)


        metrics_improvements.append([f"{value:.2f}%" for value in improvements.values()])

        axs[4, col].text(0.5, 0.5, '\n'.join([f"{name}: {value}" for name, value in zip(metrics_functions.keys(), metrics_improvements[-1])]), ha="center", va="center", fontsize=18)


        for row in range(5):
            axs[row, col].axis('off')


    plt.tight_layout(pad=2.0, h_pad=2.0, w_pad=0.5, rect=[0, 0, 1, 0.95])


    inspection_img_path = os.path.join(inspection_path, f"inspection_{i//5}.png")
    plt.savefig(inspection_img_path, bbox_inches='tight')
    plt.close()


    print(f"Saved {inspection_img_path} ({i+n_imgs}/{len(image_files)})")

print("All inspection images have been generated.")
