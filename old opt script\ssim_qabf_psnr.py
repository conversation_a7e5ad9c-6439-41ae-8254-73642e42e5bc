import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
import numpy as np
from Losses import MILoss  
from torch.nn.functional import mse_loss as mse
from Losses import qabf
from Losses import psnr


os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'


class ImageFusionOptimizer:
    def __init__(self, method, scene, metric, vis_path, ir_path, fused_path, output_folder, display_interval=100):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.method = method
        self.scene = scene
        self.metric = metric
        self.vis_path = vis_path
        self.ir_path = ir_path
        self.fused_path = fused_path
        self.output_folder = output_folder
        self.display_interval = display_interval
        self.lr = lr
        self.num_iterations = num_iterations
        
        self.transform = transforms.Compose([transforms.ToTensor()])

        self.vis = self._process_image(Image.open(self.vis_path))
        self.ir = self._process_image(Image.open(self.ir_path))
        self.fused = self._process_image(Image.open(self.fused_path))

        self._check_channel_dimensions()

        self.optimized_fused = nn.Parameter(self.fused.clone())
        self.optimizer = optim.Adam([self.optimized_fused], self.lr)

        self.loss_weights = {'ssim': 5.0, 'qabf': 3.0, 'psnr': 0.05}



        # 设置图像窗口的大小
        self.fig = plt.figure(figsize=(6, 4), dpi=300)

        # 两个子图的初始状态
        self.original_subplt = plt.subplot(121)
        self.optimized_subplt = plt.subplot(122)

        self.losses_ssim,  self.losses_qabf, self.losses_combine, self.losses_psnr = [], [], [], []
        self.ssim_val, self.mi_val = [], []

    def _process_image(self, image):
        image_tensor = TF.to_tensor(image).unsqueeze(0).to(self.device)
        if image_tensor.shape[1] == 3:
            image_tensor = image_tensor.mean(dim=1, keepdim=True)
        return image_tensor

    def _check_channel_dimensions(self):
        if self.vis.shape[1] != 1 or self.ir.shape[1] != 1 or self.fused.shape[1] != 1:
            raise ValueError("Input images must have a single channel.")


    def ssim_loss(self, x, y):
        return kornia.losses.ssim_loss(x, y, window_size=11)

    def psnr_loss(self, x, y, z):
        return psnr.psnr_loss(x,y,z)

    def qabf_loss(self, x, y, z):
        return 1-qabf.q_abf_approach_loss(x, y, z)
    

    def calculate_losses(self):
        ssim_loss_vis = self.ssim_loss(self.optimized_fused, self.vis)
        ssim_loss_ir = self.ssim_loss(self.optimized_fused, self.ir)
        current_ssim_loss = (ssim_loss_vis + ssim_loss_ir) / 2
        current_ssim_val = 1 - 2 * current_ssim_loss

        current_qabf_loss = self.qabf_loss(self.vis, self.ir, self.optimized_fused)

        current_psnr_loss = self.psnr_loss(self.vis, self.ir, self.optimized_fused)

        combine_loss = (self.loss_weights['ssim']    * current_ssim_loss +
                        self.loss_weights['qabf']    * current_qabf_loss +
                        self.loss_weights['psnr']    * current_psnr_loss 
                        ) / (self.loss_weights['ssim'] + self.loss_weights['qabf'] + self.loss_weights['psnr'])

        return current_ssim_loss, current_qabf_loss, combine_loss, current_psnr_loss

    def train(self):
        # Initialize the video writer
        video_path = f'{self.method}_{self.scene}_{self.metric}.mp4'
        writer = imageio.get_writer(video_path, fps=10)

        for i in range(self.num_iterations):
            ssim_loss, qabf_loss, combine_loss, psnr_loss = self.calculate_losses()

            self.optimizer.zero_grad()
            combine_loss.backward()
            self.optimizer.step()

            self.losses_ssim.append(ssim_loss.item())
            self.losses_qabf.append(qabf_loss.item())
            self.losses_psnr.append(psnr_loss.item())
            self.losses_combine.append(combine_loss.item())

            if i % self.display_interval == 0:
                print(f"Iteration {i + 100}")
                print(f"SSIM_loss: {ssim_loss.item()}")
                print(f"qabf_loss: {qabf_loss.item()}")
                print(f"psnr_loss: {psnr_loss.item()}")
                print(f"Combine_loss: {combine_loss.item()}")
                print('\n')

                optimized_image = self.optimized_fused.detach().clone().clamp(0, 1)
                optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
                optimized_image_display = optimized_image_display.astype(np.uint8)
                optimized_image_path = f"{self.output_folder}{self.method}_{self.scene}_{self.metric}_{i + 100}.png"

                # 创建文件夹（如果不存在）
                os.makedirs(self.output_folder, exist_ok=True)

                optimized_pil_image = TF.to_pil_image(optimized_image.squeeze(0).cpu())
                optimized_pil_image.save(optimized_image_path)

                # 显示图像
                self.original_subplt.clear()
                self.optimized_subplt.clear()

                self.original_subplt.imshow(self.fused.squeeze(0).cpu().numpy()[0], cmap='gray')
                self.optimized_subplt.imshow(optimized_image_display, cmap='gray')

                # 设置标题和关闭坐标轴
                self.original_subplt.set_title("Original Fused Image")
                self.optimized_subplt.set_title(f"Iteration: {i + 100}")
                self.original_subplt.axis('off')
                self.optimized_subplt.axis('off')

                # 刷新图像
                plt.pause(0.1)

                # 添加图像到视频
                writer.append_data(plt.imread(optimized_image_path))

        # 关闭图像窗口
        plt.close(self.fig)

        # 关闭视频写入器
        writer.close()
        print(f"Video saved to {video_path}")

    def plot_losses(self):
        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_ssim, label=f'SSIM Loss')
        plt.title(f'SSIM Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'SSIM Loss')
        plt.legend()

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_qabf, label=f'Qabf Loss')
        plt.title(f'Qabf Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'Qabf Loss')
        plt.legend()

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_qabf, label=f'psnr Loss')
        plt.title(f'psnr Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'psnr Loss')
        plt.legend()

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_combine, label=f'Combine Loss')
        plt.title(f'Combine Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'Combine Loss')
        plt.legend()

        # plt.figure(figsize=(8, 5))
        # plt.plot(self.mi_val, label=f'{self.metric} Value')
        # plt.title(f'{self.metric} Value')
        # plt.xlabel('Iteration')
        # plt.ylabel(f'{self.metric} Value')
        # plt.legend()

        plt.show()


# 使用 ImageFusionOptimizer 类
method = 'CBF'
lr = 0.0001
num_iterations = 10000
scene = 'jeep'
metric = 'Combine_ssim1+qabf1+psnr1'
vis_path = f'input/VIS/{scene}.bmp'
ir_path = f'input/IR/{scene}.bmp'
fused_path = f'input/Fused/{scene}_{method}.png'
output_folder = f"../vgg-tensorflow-master/images/{scene}/{metric}/{method}/"

fusion_optimizer = ImageFusionOptimizer(method, scene, metric, vis_path, ir_path, fused_path, output_folder)
fusion_optimizer.train()
fusion_optimizer.plot_losses()
