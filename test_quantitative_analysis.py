#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定量分析程序
"""

import os
import sys

def test_imports():
    """测试所有必要的导入"""
    print("测试导入...")
    
    try:
        import torch
        print("✓ PyTorch 导入成功")
    except ImportError as e:
        print(f"✗ PyTorch 导入失败: {e}")
        return False
    
    try:
        import torchvision
        print("✓ Torchvision 导入成功")
    except ImportError as e:
        print(f"✗ Torchvision 导入失败: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✓ Matplotlib 导入成功")
    except ImportError as e:
        print(f"✗ Matplotlib 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ Pandas 导入成功")
    except ImportError as e:
        print(f"✗ Pandas 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ NumPy 导入成功")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return False
    
    try:
        from tqdm import tqdm
        print("✓ tqdm 导入成功")
    except ImportError as e:
        print(f"✗ tqdm 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ PIL 导入成功")
    except ImportError as e:
        print(f"✗ PIL 导入失败: {e}")
        return False
    
    return True

def test_paths():
    """测试路径是否存在"""
    print("\n测试路径...")
    
    # 测试输入路径
    test_paths = [
        'input4table/VIS4val/',
        'input4table/IR4val/',
        'input4table/Fused/ADF_val/'
    ]
    
    all_exist = True
    for path in test_paths:
        if os.path.exists(path):
            print(f"✓ 路径存在: {path}")
        else:
            print(f"✗ 路径不存在: {path}")
            all_exist = False
    
    return all_exist

def test_losses_module():
    """测试Losses模块"""
    print("\n测试Losses模块...")
    
    try:
        from Losses import ssim
        print("✓ ssim 导入成功")
    except ImportError as e:
        print(f"✗ ssim 导入失败: {e}")
        return False
    
    try:
        from Losses import sf
        print("✓ sf 导入成功")
    except ImportError as e:
        print(f"✗ sf 导入失败: {e}")
        return False
    
    try:
        from Losses import qabf
        print("✓ qabf 导入成功")
    except ImportError as e:
        print(f"✗ qabf 导入失败: {e}")
        return False
    
    try:
        from Losses import vif
        print("✓ vif 导入成功")
    except ImportError as e:
        print(f"✗ vif 导入失败: {e}")
        return False
    
    try:
        from Losses import scd
        print("✓ scd 导入成功")
    except ImportError as e:
        print(f"✗ scd 导入失败: {e}")
        return False
    
    try:
        from Losses import mi
        print("✓ mi 导入成功")
    except ImportError as e:
        print(f"✗ mi 导入失败: {e}")
        return False
    
    try:
        from Losses import cc
        print("✓ cc 导入成功")
    except ImportError as e:
        print(f"✗ cc 导入失败: {e}")
        return False
    
    try:
        from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric, ei_metric
        print("✓ 所有指标函数导入成功")
    except ImportError as e:
        print(f"✗ 指标函数导入失败: {e}")
        return False
    
    return True

def test_device():
    """测试设备"""
    print("\n测试设备...")
    
    try:
        import torch
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"✓ 使用设备: {device}")
        
        if torch.cuda.is_available():
            print(f"✓ CUDA 可用, GPU: {torch.cuda.get_device_name()}")
        else:
            print("! 使用CPU (建议使用GPU以获得更好性能)")
        
        return True
    except Exception as e:
        print(f"✗ 设备测试失败: {e}")
        return False

def test_sample_images():
    """测试样本图像"""
    print("\n测试样本图像...")
    
    scene = '00280'
    method = 'ADF_val'
    
    vis_path = f'input4table/VIS4val/{scene}.png'
    ir_path = f'input4table/IR4val/{scene}.png'
    fused_path = f'input4table/Fused/{method}/{scene}.png'
    
    paths = [vis_path, ir_path, fused_path]
    path_names = ['VIS图像', 'IR图像', '融合图像']
    
    all_exist = True
    for path, name in zip(paths, path_names):
        if os.path.exists(path):
            print(f"✓ {name}存在: {path}")
        else:
            print(f"✗ {name}不存在: {path}")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 60)
    print("定量分析程序环境测试")
    print("=" * 60)
    
    tests = [
        ("基础库导入", test_imports),
        ("路径检查", test_paths),
        ("Losses模块", test_losses_module),
        ("设备检查", test_device),
        ("样本图像", test_sample_images)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<15}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！可以运行定量分析程序。")
        print("\n运行命令: python opt_quantitative_analysis.py")
    else:
        print("❌ 部分测试失败，请先解决上述问题。")
        print("\n建议:")
        print("1. 确保所有必要的Python包已安装")
        print("2. 检查输入图像路径是否正确")
        print("3. 确保Losses模块在当前目录")
    print("=" * 60)

if __name__ == "__main__":
    main()
