#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版定量分析测试程序
用于验证基本功能是否正常
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms.functional as TF
from PIL import Image
import os
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm

# 检查是否能导入Losses模块
try:
    from Losses import ssim, sf, qabf, vif, scd, mi, cc
    from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric, ei_metric
    print("✓ Losses模块导入成功")
except ImportError as e:
    print(f"✗ Losses模块导入失败: {e}")
    exit(1)

# 设置随机种子
seed = 42
torch.manual_seed(seed)
np.random.seed(seed)

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 用户选择的参数
method = 'ADF_val'
scene = '00280'
metric = 'lr0.003'

# 图像路径
vis_path = f'input4table/VIS4val/{scene}.png'
ir_path = f'input4table/IR4val/{scene}.png'
fused_path = f'input4table/Fused/{method}/{scene}.png'

# 检查文件是否存在
paths_to_check = [vis_path, ir_path, fused_path]
path_names = ['VIS图像', 'IR图像', '融合图像']

for path, name in zip(paths_to_check, path_names):
    if os.path.exists(path):
        print(f"✓ {name}存在: {path}")
    else:
        print(f"✗ {name}不存在: {path}")
        exit(1)

# 简化的测试参数
lr = 0.003
image_sizes = [128, 256]  # 只测试两个尺寸
iteration_counts = [50, 100]  # 只测试两个迭代次数

# 创建输出文件夹
output_folder = f"./test_quantitative/{scene}_{method}/"
os.makedirs(output_folder, exist_ok=True)

def resize_image(image_tensor, size):
    """将图像张量调整到指定尺寸"""
    return TF.resize(image_tensor, (size, size), antialias=True)

def read_and_resize_image(path, size):
    """读取图像并调整到指定尺寸"""
    image = TF.to_tensor(Image.open(path)).unsqueeze(0).to(device)
    if image.shape[1] == 3:
        image = image.mean(dim=1, keepdim=True)
    return resize_image(image, size)

def calculate_metrics_absolute(vis, ir, fused):
    """计算指标绝对值"""
    metrics = {}
    metrics['SSIM'] = ssim_metric(vis, ir, fused).item()
    metrics['MI'] = mi_metric(vis, ir, fused).item()
    metrics['Qabf'] = q_abf_metric(vis, ir, fused).item()
    metrics['SF'] = sf_metric(vis, ir, fused).item()
    metrics['VIF'] = vif_metric(vis, ir, fused).item()
    metrics['CC'] = cc_metric(vis, ir, fused).item()
    return metrics

def simple_loss_function(x, y, vis, ir):
    """简化的损失函数"""
    ssim_diff = (ssim.ssim_metric(vis, ir, y) - ssim.ssim_metric(vis, ir, x)) / ssim.ssim_metric(vis, ir, x) 
    mi_diff = (mi.mi_metric(vis, ir, y) - mi.mi_metric(vis, ir, x)) / mi.mi_metric(vis, ir, x) 
    
    def mapping_function(diff):
        return torch.tanh(5.5 * diff)
    
    ssim_mapped = mapping_function(ssim_diff)
    mi_mapped = mapping_function(mi_diff)
    
    loss = (ssim_mapped + mi_mapped * 0.1) / 2
    return loss

def test_optimization(size, num_iterations):
    """测试单个尺寸和迭代次数组合"""
    print(f"\n测试 {size}x{size} 图像, {num_iterations} 次迭代")
    
    # 读取并调整图像尺寸
    vis = read_and_resize_image(vis_path, size)
    ir = read_and_resize_image(ir_path, size)
    fused = read_and_resize_image(fused_path, size)
    
    # 创建优化参数
    optimized_fused = nn.Parameter(fused.clone()).to(device)
    optimizer = optim.Adam([optimized_fused], lr)
    
    # 记录数据
    losses = []
    running_times = []
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 优化迭代
    pbar = tqdm(range(num_iterations + 1), desc=f"{size}x{size}, {num_iterations}iter")
    
    for i in pbar:
        iter_start_time = time.time()
        
        # 计算损失
        current_loss = simple_loss_function(optimized_fused, fused, vis, ir)
        
        # 反向传播和优化
        optimizer.zero_grad()
        current_loss.backward()
        optimizer.step()
        
        # 记录时间和损失
        iter_time = time.time() - iter_start_time
        running_times.append(iter_time)
        losses.append(current_loss.item())
        
        # 更新进度条
        if i % 10 == 0:
            pbar.set_postfix({
                'Loss': f'{current_loss.item():.6f}',
                'Time': f'{iter_time:.3f}s'
            })
    
    pbar.close()
    
    # 计算总时间
    total_time = time.time() - total_start_time
    
    # 计算指标
    original_metrics = calculate_metrics_absolute(vis, ir, fused)
    final_metrics = calculate_metrics_absolute(vis, ir, optimized_fused)
    
    # 保存最终图像
    final_image = optimized_fused.detach().clone().clamp(0, 1)
    final_path = f"{output_folder}{scene}_{size}x{size}_{num_iterations}iter_final.png"
    TF.to_pil_image(final_image.squeeze(0).cpu()).save(final_path)
    
    result = {
        'size': size,
        'num_iterations': num_iterations,
        'total_time': total_time,
        'avg_time_per_iter': np.mean(running_times),
        'final_loss': losses[-1],
        'original_metrics': original_metrics,
        'final_metrics': final_metrics,
        'losses': losses
    }
    
    print(f"完成! 总时间: {total_time:.2f}s, 平均: {np.mean(running_times):.4f}s/iter")
    print(f"SSIM: {original_metrics['SSIM']:.4f} -> {final_metrics['SSIM']:.4f}")
    
    return result

def main():
    """主测试函数"""
    print("开始简化版定量分析测试...")
    print(f"Method: {method}, Scene: {scene}")
    print(f"测试尺寸: {image_sizes}")
    print(f"迭代次数: {iteration_counts}")
    
    results = []
    
    # 对每个尺寸和每个迭代次数进行测试
    for size in image_sizes:
        for num_iterations in iteration_counts:
            result = test_optimization(size, num_iterations)
            results.append(result)
    
    # 创建简单的结果表格
    print("\n" + "="*80)
    print("测试结果汇总")
    print("="*80)
    
    print(f"{'Size':<12} {'Iterations':<12} {'Total Time':<12} {'Avg Time/Iter':<15} {'SSIM Imp.':<12}")
    print("-" * 75)
    
    for result in results:
        size = result['size']
        num_iter = result['num_iterations']
        total_time = result['total_time']
        avg_time = result['avg_time_per_iter']
        ssim_imp = result['final_metrics']['SSIM'] - result['original_metrics']['SSIM']
        
        print(f"{size}x{size:<8} {num_iter:<12} {total_time:<12.2f} {avg_time:<15.6f} {ssim_imp:<12.6f}")
    
    # 保存结果到Excel
    analysis_data = []
    for result in results:
        analysis_data.append({
            'Size': f"{result['size']}x{result['size']}",
            'Iterations': result['num_iterations'],
            'Total_Time(s)': result['total_time'],
            'Avg_Time_Per_Iter(s)': result['avg_time_per_iter'],
            'Final_Loss': result['final_loss'],
            'Original_SSIM': result['original_metrics']['SSIM'],
            'Final_SSIM': result['final_metrics']['SSIM'],
            'SSIM_Improvement': result['final_metrics']['SSIM'] - result['original_metrics']['SSIM']
        })
    
    df = pd.DataFrame(analysis_data)
    excel_path = f"{output_folder}test_results.xlsx"
    df.to_excel(excel_path, index=False)
    print(f"\n结果已保存到: {excel_path}")
    
    # 创建简单的可视化
    plt.figure(figsize=(12, 8))
    
    # 子图1: 时间对比
    plt.subplot(2, 2, 1)
    labels = [f"{r['size']}x{r['size']}\n{r['num_iterations']}iter" for r in results]
    times = [r['total_time'] for r in results]
    plt.bar(range(len(results)), times)
    plt.xlabel('Size x Iterations')
    plt.ylabel('Total Time (s)')
    plt.title('Total Time Comparison')
    plt.xticks(range(len(results)), labels, rotation=45)
    
    # 子图2: 平均时间对比
    plt.subplot(2, 2, 2)
    avg_times = [r['avg_time_per_iter'] for r in results]
    plt.bar(range(len(results)), avg_times)
    plt.xlabel('Size x Iterations')
    plt.ylabel('Avg Time per Iter (s)')
    plt.title('Average Time per Iteration')
    plt.xticks(range(len(results)), labels, rotation=45)
    
    # 子图3: SSIM改善对比
    plt.subplot(2, 2, 3)
    ssim_improvements = [r['final_metrics']['SSIM'] - r['original_metrics']['SSIM'] for r in results]
    plt.bar(range(len(results)), ssim_improvements)
    plt.xlabel('Size x Iterations')
    plt.ylabel('SSIM Improvement')
    plt.title('SSIM Improvement')
    plt.xticks(range(len(results)), labels, rotation=45)
    
    # 子图4: 损失收敛曲线
    plt.subplot(2, 2, 4)
    colors = ['red', 'blue', 'green', 'orange']
    for i, result in enumerate(results):
        losses = result['losses']
        label = f"{result['size']}x{result['size']}, {result['num_iterations']}iter"
        plt.plot(losses, color=colors[i], label=label, alpha=0.8)
    plt.xlabel('Iteration')
    plt.ylabel('Loss')
    plt.title('Loss Convergence')
    plt.legend(fontsize=8)
    plt.yscale('log')
    
    plt.tight_layout()
    chart_path = f"{output_folder}test_results_chart.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"图表已保存到: {chart_path}")
    
    print("\n测试完成!")
    return results

if __name__ == "__main__":
    results = main()
