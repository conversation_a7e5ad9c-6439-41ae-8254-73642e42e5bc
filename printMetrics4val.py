#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打印指标程序 v2.0
新版本特性：
1. 使用文件选择窗口选择融合图像目录
2. 固定的红外和可见光原图路径
3. 通过文件名匹配对应的三张图像
4. 批量计算所有匹配图像的指标
"""

import cv2
import torchvision.transforms.functional as TF
from PIL import Image
import numpy as np
import torch
import os
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
from pathlib import Path
import glob

# 导入指标计算函数
from Losses import ce_metric
from Losses import en_metric
from Losses import mi_metric
from Losses import ag_metric
from Losses import ei_metric
from Losses import psnr_metric
from Losses import q_abf_metric
from Losses import sd_metric
from Losses import rmse_metric
from Losses import ssim_metric
from Losses import sf_metric
from Losses import q_cb_metric
from Losses import scd_metric
from Losses import vif_metric
from Losses import cc_metric


class MetricsCalculator:
    def __init__(self):
        # 固定的红外和可见光原图路径
        self.ir_base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\IR4val"
        self.vis_base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\VIS4val"
        
        # 定义指标名称和计算函数的字典
        self.metrics_functions = {
            'CE': ce_metric,
            'EN': en_metric,
            'MI': mi_metric, 
            'PSNR': psnr_metric,
            'EI': ei_metric,
            'Qabf': q_abf_metric,
            'SD': sd_metric,
            'SF': sf_metric,
            'RMSE': rmse_metric,
            'SSIM': ssim_metric,
            'Qcb': q_cb_metric,
            'VIF': vif_metric,
            'SCD': scd_metric,
            'CC': cc_metric
        }
        
        self.results = []
    
    def select_fusion_directory(self):
        """使用文件选择窗口选择融合图像目录"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        directory = filedialog.askdirectory(
            title="选择融合图像目录",
            initialdir=r"F:\.Infrared and Visiable Light Fusion"
        )
        
        root.destroy()
        return directory
    
    def read_image(self, path):
        """读取图像并转化为灰度图像"""
        try:
            # 使用PIL读取图像
            img = Image.open(path)
            # 转换为灰度图像（如果不是灰度图像）
            if img.mode != 'L':
                img = img.convert('L')
            # 转换为tensor并添加batch维度
            img_tensor = TF.to_tensor(img).unsqueeze(0)
            # 移动到GPU（如果可用）
            if torch.cuda.is_available():
                return img_tensor.cuda()
            else:
                return img_tensor
        except Exception as e:
            print(f"读取图像失败: {path}")
            print(f"错误: {e}")
            return None
    
    def resize_images_to_same_size(self, *images):
        """调整所有图像到相同尺寸（以第一个图像为基准）"""
        if not images or images[0] is None:
            return images
        
        # 获取基准图像的尺寸
        reference_h, reference_w = images[0].shape[-2], images[0].shape[-1]
        
        resized_images = []
        for img in images:
            if img is None:
                resized_images.append(None)
                continue
                
            if img.shape[-2] != reference_h or img.shape[-1] != reference_w:
                img_resized = torch.nn.functional.interpolate(
                    img, 
                    size=(reference_h, reference_w), 
                    mode='bilinear', 
                    align_corners=False
                )
                resized_images.append(img_resized)
            else:
                resized_images.append(img)
        
        return tuple(resized_images)
    
    def calculate_metrics(self, vis, ir, fused):
        """计算所有指标"""
        metrics = {}
        for metric_name, metric_func in self.metrics_functions.items():
            try:
                result = metric_func(vis, ir, fused)
                if torch.cuda.is_available():
                    metrics[metric_name] = result.cuda()
                else:
                    metrics[metric_name] = result
            except Exception as e:
                print(f"计算指标 {metric_name} 时出错: {e}")
                metrics[metric_name] = torch.tensor(0.0)
        
        return metrics
    
    def get_matching_files(self, fusion_dir):
        """获取所有匹配的文件组合"""
        # 获取融合图像目录中的所有图像文件
        fusion_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']:
            fusion_files.extend(glob.glob(os.path.join(fusion_dir, ext)))
        
        matching_files = []
        
        for fusion_path in fusion_files:
            # 获取文件名（不含扩展名）
            filename = Path(fusion_path).stem
            
            # 构建对应的红外和可见光图像路径
            ir_path = os.path.join(self.ir_base_path, f"{filename}.png")
            vis_path = os.path.join(self.vis_base_path, f"{filename}.png")
            
            # 检查文件是否存在
            if os.path.exists(ir_path) and os.path.exists(vis_path):
                matching_files.append({
                    'filename': filename,
                    'ir_path': ir_path,
                    'vis_path': vis_path,
                    'fusion_path': fusion_path
                })
            else:
                print(f"警告: 找不到匹配的文件 {filename}")
        
        return matching_files
    
    def process_single_image_set(self, file_info):
        """处理单组图像并计算指标"""
        filename = file_info['filename']
        print(f"处理图像: {filename}")
        
        # 读取三张图像
        ir_img = self.read_image(file_info['ir_path'])
        vis_img = self.read_image(file_info['vis_path'])
        fusion_img = self.read_image(file_info['fusion_path'])
        
        if ir_img is None or vis_img is None or fusion_img is None:
            print(f"跳过图像 {filename}: 读取失败")
            return None
        
        # 调整图像尺寸
        vis_img, ir_img, fusion_img = self.resize_images_to_same_size(vis_img, ir_img, fusion_img)
        
        # 计算指标
        metrics = self.calculate_metrics(vis_img, ir_img, fusion_img)
        
        # 准备结果数据
        result = {'Filename': filename}
        for metric_name, metric_value in metrics.items():
            if torch.is_tensor(metric_value):
                result[metric_name] = float(metric_value.cpu().detach().numpy())
            else:
                result[metric_name] = float(metric_value)
        
        return result
    
    def run(self):
        """主运行函数"""
        print("=== 打印指标程序 v2.0 ===")
        print(f"红外图像路径: {self.ir_base_path}")
        print(f"可见光图像路径: {self.vis_base_path}")
        
        # 检查固定路径是否存在
        if not os.path.exists(self.ir_base_path):
            messagebox.showerror("错误", f"红外图像路径不存在: {self.ir_base_path}")
            return
        
        if not os.path.exists(self.vis_base_path):
            messagebox.showerror("错误", f"可见光图像路径不存在: {self.vis_base_path}")
            return
        
        # 选择融合图像目录
        fusion_dir = self.select_fusion_directory()
        if not fusion_dir:
            print("未选择目录，程序退出")
            return
        
        print(f"融合图像路径: {fusion_dir}")
        
        # 获取匹配的文件
        matching_files = self.get_matching_files(fusion_dir)
        
        if not matching_files:
            messagebox.showwarning("警告", "没有找到匹配的图像文件")
            return
        
        print(f"找到 {len(matching_files)} 组匹配的图像")
        
        # 处理所有图像
        print("\n开始处理图像...")
        for i, file_info in enumerate(matching_files, 1):
            print(f"进度: {i}/{len(matching_files)}")
            result = self.process_single_image_set(file_info)
            if result:
                self.results.append(result)
        
        # 保存结果
        if self.results:
            self.save_results(fusion_dir)
            print(f"\n处理完成! 成功处理了 {len(self.results)} 组图像")
        else:
            print("没有成功处理任何图像")
    
    def save_results(self, fusion_dir):
        """保存结果到Excel文件"""
        df = pd.DataFrame(self.results)
        
        # 生成输出文件名
        fusion_dir_name = Path(fusion_dir).name
        output_filename = f"metrics_results_{fusion_dir_name}.xlsx"
        
        # 保存到Excel
        df.to_excel(output_filename, index=False)
        print(f"结果已保存到: {output_filename}")
        
        # 计算平均值
        numeric_columns = [col for col in df.columns if col != 'Filename']
        avg_results = df[numeric_columns].mean()
        
        print("\n平均指标值:")
        for metric_name, avg_value in avg_results.items():
            print(f"{metric_name}: {avg_value:.4f}")
        
        # 保存平均值到单独的Excel文件
        avg_df = pd.DataFrame([avg_results])
        avg_filename = f"metrics_average_{fusion_dir_name}.xlsx"
        avg_df.to_excel(avg_filename, index=False)
        print(f"平均值已保存到: {avg_filename}")


if __name__ == "__main__":
    try:
        calculator = MetricsCalculator()
        calculator.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
