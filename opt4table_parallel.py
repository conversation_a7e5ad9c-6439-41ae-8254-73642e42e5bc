import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import os
import time
import imageio
import numpy as np
from Losses import ssim
from Losses import mi
from Losses import qabf
from Losses import psnr
from Losses import sf
from Losses import sd
from Losses import vif
from Losses import scd
from Losses import cc
from displayMetrics import display_metrics
from PIL import Image
from tqdm import tqdm
import kornia

# 锁随机种子
seed = 42
torch.manual_seed(seed)
np.random.seed(seed)

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 设置输入和输出路径
input_folder = "./input4table"
output_folder_base = "./output"

# 获取所有的融合方法
fusion_methods = ["ADF"]  # 可加入多个方法

def monotone_incresing_loss(x, y, vis, ir): 
    ssim_diff = (ssim.ssim_metric(vis, ir, y) - ssim.ssim_metric(vis, ir, x)) / ssim.ssim_metric(vis, ir, x) 
    mi_diff = (mi.mi_metric(vis, ir, y) - mi.mi_metric(vis, ir, x)) / mi.mi_metric(vis, ir, x) 
    qabf_diff = (qabf.q_abf_metric(vis, ir, y) - qabf.q_abf_metric(vis, ir, x)) / qabf.q_abf_metric(vis, ir, x) 
    psnr_diff = (psnr.psnr_metric(vis, ir, y) - psnr.psnr_metric(vis, ir, x)) / psnr.psnr_metric(vis, ir, x) 
    sf_diff = (sf.sf_metric(vis, ir, y) - sf.sf_metric(vis, ir, x)) / sf.sf_metric(vis, ir, x) 
    sd_diff = (sd.sd_metric(vis, ir, y) - sd.sd_metric(vis, ir, x)) / sd.sd_metric(vis, ir, x)
    vif_diff = (vif.vif_metric(vis, ir, y) - vif.vif_metric(vis, ir, x)) / vif.vif_metric(vis, ir, x)
    scd_diff = (scd.scd_metric(vis, ir, y) - scd.scd_metric(vis, ir, x)) / scd.scd_metric(vis, ir, x)
    cc_diff = (cc.cc_metric(vis, ir, y) - cc.cc_metric(vis, ir, x)) / cc.cc_metric(vis, ir, x)

    def mapping_function(diff):
        mapped_diff = (torch.exp(5.5 * diff) - torch.exp(5.5 * -diff))/ (torch.exp(5.5 * diff) + torch.exp(5.5 * -diff))
        return mapped_diff

    ssim_mapped = mapping_function(ssim_diff)
    mi_mapped = mapping_function(mi_diff)
    qabf_mapped = mapping_function(qabf_diff)
    psnr_mapped = mapping_function(psnr_diff)
    sf_mapped = mapping_function(sf_diff)
    sd_mapped = mapping_function(sd_diff)
    vif_mapped = mapping_function(vif_diff)
    scd_mapped = mapping_function(scd_diff)
    cc_mapped = mapping_function(cc_diff)

    # loss = ( 
    #     ssim_mapped +
    #     mi_mapped +  
    #     qabf_mapped + 
    #     psnr_mapped +
    #     sf_mapped  +
    #     sd_mapped +
    #     vif_mapped +
    #     scd_mapped +
    #     cc_mapped  

    #     ) / 9
    
    loss = ( 
        ssim_mapped +
        mi_mapped * 0.01 +  
        qabf_mapped * 0.5 + 
        # psnr_mapped +
        sf_mapped * 0.1 +
        # sd_mapped +
        vif_mapped +
        scd_mapped +
        cc_mapped  

        ) / 6
    
    
    return loss

def histogram2d(x1, x2, bins, bandwidth):
    bandwidth.requires_grad_(True)  # 设置需要梯度
    hist = kornia.enhance.histogram2d(x1, x2, bins, bandwidth=bandwidth)
    return hist


# 创建一个transform来保留原始尺寸并转换为张量
transform = transforms.Compose([
    transforms.ToTensor()
])

# 设置Batch Size
batch_size = 4

for method in fusion_methods:
    input_fused_folder = os.path.join(input_folder, "Fused", method)
    output_folder_method = os.path.join(output_folder_base, method)
    os.makedirs(output_folder_method, exist_ok=True)
    
    # 获取当前融合方法下的所有融合图像文件
    fused_images = [f for f in os.listdir(input_fused_folder) if f.endswith(f"_{method}.png")]
    
    # 获取输出文件夹中已有的优化图像文件
    existing_optimized_images = set(os.listdir(output_folder_method))

    # 收集要优化的图像并组织成批次
    for idx in range(0, len(fused_images), batch_size):
        batch_files = fused_images[idx:idx+batch_size]
        batch_fused_images = []
        batch_vis_images = []
        batch_ir_images = []

        for fused_image_name in batch_files:
            fused_path = os.path.join(input_fused_folder, fused_image_name)
            fused = transform(Image.open(fused_path)).unsqueeze(0).to(device)

            vis_name = fused_image_name.split("_")[0] + ".png"
            ir_name = fused_image_name.split("_")[0] + ".png"
            vis_path = os.path.join(input_folder, "VIS", vis_name)
            ir_path = os.path.join(input_folder, "IR", ir_name)

            vis = transform(Image.open(vis_path)).unsqueeze(0).to(device)
            ir = transform(Image.open(ir_path)).unsqueeze(0).to(device)

            batch_fused_images.append(fused)
            batch_vis_images.append(vis)
            batch_ir_images.append(ir)

        batch_fused = torch.cat(batch_fused_images, dim=0)
        batch_vis = torch.cat(batch_vis_images, dim=0)
        batch_ir = torch.cat(batch_ir_images, dim=0)

        # 需要梯度的张量
        batch_fused = batch_fused.clone().detach().requires_grad_(True)

        # 优化器和损失函数
        lr = 0.003
        num_iterations = 200
        optimizer = optim.Adam([batch_fused], lr)

        losses = []
        start_time = time.time()
        print("=============================================")
        print(f"Optimizing images in batch {idx // batch_size + 1}...")

        with tqdm(total=num_iterations) as pbar:
            for i in range(num_iterations + 1):
                current_loss = monotone_incresing_loss(batch_fused, batch_fused, batch_vis, batch_ir)
                optimizer.zero_grad()
                current_loss.backward()
                optimizer.step()
                losses.append(current_loss.item())

                # 显示优化进度条
                pbar.update(1)
                pbar.set_description(f"Iter {i}/{num_iterations}")

        end_time = time.time()
        total_time = end_time - start_time
        average_time = total_time / (num_iterations + 1)
        print("---------------------------------------------")
        print(f"Batch optimization finished.")
        print("---------------------------------------------")
        print(f"Average time per iteration: {average_time:.2f} seconds")
        print(f"Total time: {total_time:.2f} seconds")
        print("=============================================")
        print("\n")

        # 保存优化后的图像
        optimized_images = batch_fused.detach().clone().clamp(0, 1)
        for idx, optimized_image in enumerate(optimized_images):
            optimized_image_path = os.path.join(output_folder_method, batch_files[idx].replace(".png", f"_opt.png"))
            transforms.ToPILImage()(optimized_image.cpu()).save(optimized_image_path)
