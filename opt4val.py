#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量优化脚本
基于opt_online.py模板，对指定算法文件夹下的所有融合图进行批量优化
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms.functional as TF
from PIL import Image
import os
import time
import numpy as np
import glob
from pathlib import Path
from tqdm import tqdm

# 导入损失函数
from Losses import ssim
from Losses import psnr
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import vif
from Losses import scd
from Losses import mi
from Losses import cc
from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric

# 锁随机种子
seed = 42
torch.manual_seed(seed)
np.random.seed(seed)

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

class BatchOptimizer:
    def __init__(self, algorithm_name, lr=0.003, num_iterations=200):
        self.algorithm_name = algorithm_name
        self.lr = lr
        self.num_iterations = num_iterations
        
        # 设置路径
        self.ir_base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\IR4val"
        self.vis_base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\VIS4val"
        self.fused_base_path = rf"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\Fused\{algorithm_name}"
        self.output_base_path = rf"F:\.Infrared and Visiable Light Fusion\Optimization based\output\{algorithm_name}"
        
        # 创建输出目录
        os.makedirs(self.output_base_path, exist_ok=True)
        
        print(f"算法: {algorithm_name}")
        print(f"红外图路径: {self.ir_base_path}")
        print(f"可见光图路径: {self.vis_base_path}")
        print(f"融合图路径: {self.fused_base_path}")
        print(f"输出路径: {self.output_base_path}")
        print(f"学习率: {lr}, 迭代次数: {num_iterations}")
    
    def find_matching_images(self):
        """查找所有匹配的图像组合"""
        # 获取融合图像目录中的所有图像文件
        fusion_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']:
            fusion_files.extend(glob.glob(os.path.join(self.fused_base_path, ext)))
        
        matching_files = []
        
        for fusion_path in fusion_files:
            # 获取文件名（不含扩展名）
            filename = Path(fusion_path).stem
            
            # 构建对应的红外和可见光图像路径
            ir_path = os.path.join(self.ir_base_path, f"{filename}.png")
            vis_path = os.path.join(self.vis_base_path, f"{filename}.png")
            
            # 检查文件是否存在
            if os.path.exists(ir_path) and os.path.exists(vis_path):
                matching_files.append({
                    'filename': filename,
                    'ir_path': ir_path,
                    'vis_path': vis_path,
                    'fusion_path': fusion_path
                })
            else:
                print(f"警告: 找不到匹配的文件 {filename}")
        
        return matching_files
    
    def read_image(self, path):
        """读取图像并转化为灰度图像"""
        try:
            img = TF.to_tensor(Image.open(path)).unsqueeze(0).to(device)
            # 三通道变单通道
            if img.shape[1] == 3:
                img = img.mean(dim=1, keepdim=True)
            return img
        except Exception as e:
            print(f"读取图像失败: {path}, 错误: {e}")
            return None
    
    def calculate_metrics_absolute(self, vis, ir, fused):
        """计算指标绝对值"""
        metrics = {}
        try:
            metrics['CE'] = ce_metric(vis, ir, fused).item()
            metrics['EN'] = en_metric(vis, ir, fused).item()
            metrics['MI'] = mi_metric(vis, ir, fused).item()
            metrics['PSNR'] = psnr_metric(vis, ir, fused).item()
            metrics['Qabf'] = q_abf_metric(vis, ir, fused).item()
            metrics['SD'] = sd_metric(vis, ir, fused).item()
            metrics['SF'] = sf_metric(vis, ir, fused).item()
            metrics['SSIM'] = ssim_metric(vis, ir, fused).item()
            metrics['VIF'] = vif_metric(vis, ir, fused).item()
            metrics['SCD'] = scd_metric(vis, ir, fused).item()
            metrics['CC'] = cc_metric(vis, ir, fused).item()
        except Exception as e:
            print(f"计算指标时出错: {e}")
            # 返回默认值
            for metric in ['CE', 'EN', 'MI', 'PSNR', 'Qabf', 'SD', 'SF', 'SSIM', 'VIF', 'SCD', 'CC']:
                metrics[metric] = 0.0
        
        return metrics
    
    def monotone_increasing_loss(self, x, y, vis, ir):
        """单调递增损失函数"""
        # 计算各指标的相对差异
        ssim_diff = (ssim.ssim_metric(vis, ir, y) - ssim.ssim_metric(vis, ir, x)) / ssim.ssim_metric(vis, ir, x) 
        mi_diff = (mi.mi_metric(vis, ir, y) - mi.mi_metric(vis, ir, x)) / mi.mi_metric(vis, ir, x) 
        qabf_diff = (qabf.q_abf_metric(vis, ir, y) - qabf.q_abf_metric(vis, ir, x)) / qabf.q_abf_metric(vis, ir, x) 
        psnr_diff = (psnr.psnr_metric(vis, ir, y) - psnr.psnr_metric(vis, ir, x)) / psnr.psnr_metric(vis, ir, x) 
        sf_diff = (sf.sf_metric(vis, ir, y) - sf.sf_metric(vis, ir, x)) / sf.sf_metric(vis, ir, x) 
        sd_diff = (sd.sd_metric(vis, ir, y) - sd.sd_metric(vis, ir, x)) / sd.sd_metric(vis, ir, x)
        vif_diff = (vif.vif_metric(vis, ir, y) - vif.vif_metric(vis, ir, x)) / vif.vif_metric(vis, ir, x)
        scd_diff = (scd.scd_metric(vis, ir, y) - scd.scd_metric(vis, ir, x)) / scd.scd_metric(vis, ir, x)
        cc_diff = (cc.cc_metric(vis, ir, y) - cc.cc_metric(vis, ir, x)) / cc.cc_metric(vis, ir, x)

        # 映射函数
        def mapping_function(diff):
            mapped_diff = (torch.exp(5.5 * diff) - torch.exp(5.5 * -diff))/ (torch.exp(5.5 * diff) + torch.exp(5.5 * -diff))
            return mapped_diff

        # 应用映射函数
        ssim_mapped = mapping_function(ssim_diff)
        mi_mapped = mapping_function(mi_diff)
        qabf_mapped = mapping_function(qabf_diff)
        psnr_mapped = mapping_function(psnr_diff)
        sf_mapped = mapping_function(sf_diff)
        sd_mapped = mapping_function(sd_diff)
        vif_mapped = mapping_function(vif_diff)
        scd_mapped = mapping_function(scd_diff)
        cc_mapped = mapping_function(cc_diff)

        # 计算损失
        loss = ( 
            ssim_mapped +
            mi_mapped * 0.01 +  
            qabf_mapped * 0.5 + 
            sf_mapped * 0.1 +
            vif_mapped +
            scd_mapped +
            cc_mapped  
        ) / 7
        
        return loss
    
    def optimize_single_image(self, file_info):
        """优化单张图像"""
        filename = file_info['filename']
        
        # 读取图像
        vis = self.read_image(file_info['vis_path'])
        ir = self.read_image(file_info['ir_path'])
        fused = self.read_image(file_info['fusion_path'])
        
        if vis is None or ir is None or fused is None:
            print(f"跳过图像 {filename}: 读取失败")
            return False
        
        # 计算原始指标
        original_metrics = self.calculate_metrics_absolute(vis, ir, fused)
        
        # 初始化优化参数
        optimized_fused = nn.Parameter(fused.clone()).to(device)
        optimizer = optim.Adam([optimized_fused], self.lr)
        
        # 优化循环
        print(f"\n优化图像: {filename}")
        progress_bar = tqdm(range(self.num_iterations + 1), desc="优化进度", ncols=100)
        
        for i in progress_bar:
            # 计算损失
            current_loss = self.monotone_increasing_loss(optimized_fused, fused, vis, ir)
            
            # 反向传播和优化
            optimizer.zero_grad()
            current_loss.backward()
            optimizer.step()
            
            # 更新进度条
            progress_bar.set_postfix({'Loss': f'{current_loss.item():.6f}'})
        
        progress_bar.close()
        
        # 计算优化后指标
        final_metrics = self.calculate_metrics_absolute(vis, ir, optimized_fused)
        
        # 保存优化后的图像
        final_optimized_image = optimized_fused.detach().clone().clamp(0, 1)
        output_path = os.path.join(self.output_base_path, f"{filename}.png")
        TF.to_pil_image(final_optimized_image.squeeze(0).cpu()).save(output_path)
        
        # 打印指标对比
        self.print_metrics_comparison(filename, original_metrics, final_metrics)
        
        return True
    
    def print_metrics_comparison(self, filename, original_metrics, final_metrics):
        """打印指标对比结果"""
        print(f"\n=== {filename} 指标对比 ===")
        print(f"{'指标':<8} {'原始值':<12} {'优化值':<12} {'变化':<12} {'变化率':<12}")
        print("-" * 60)
        
        for metric_name in original_metrics.keys():
            orig_val = original_metrics[metric_name]
            final_val = final_metrics[metric_name]
            diff = final_val - orig_val
            
            # 计算变化百分比
            if metric_name == 'CE':  # CE越小越好
                change_percent = -((final_val - orig_val) / orig_val) * 100 if orig_val != 0 else 0
            else:  # 其他指标越大越好
                change_percent = ((final_val - orig_val) / orig_val) * 100 if orig_val != 0 else 0
            
            print(f"{metric_name:<8} {orig_val:<12.4f} {final_val:<12.4f} {diff:<12.4f} {change_percent:<12.2f}%")
    
    def run_batch_optimization(self):
        """运行批量优化"""
        print("=== 批量优化开始 ===")
        
        # 检查路径是否存在
        if not os.path.exists(self.fused_base_path):
            print(f"错误: 融合图路径不存在: {self.fused_base_path}")
            return
        
        if not os.path.exists(self.ir_base_path):
            print(f"错误: 红外图路径不存在: {self.ir_base_path}")
            return
        
        if not os.path.exists(self.vis_base_path):
            print(f"错误: 可见光图路径不存在: {self.vis_base_path}")
            return
        
        # 查找匹配的图像
        matching_files = self.find_matching_images()
        
        if not matching_files:
            print("没有找到匹配的图像文件")
            return
        
        print(f"找到 {len(matching_files)} 组匹配的图像")
        
        # 批量优化
        success_count = 0
        start_time = time.time()
        
        for i, file_info in enumerate(matching_files, 1):
            print(f"\n{'='*50}")
            print(f"处理第 {i}/{len(matching_files)} 张图像")
            
            if self.optimize_single_image(file_info):
                success_count += 1
        
        # 总结
        total_time = time.time() - start_time
        print(f"\n{'='*50}")
        print("=== 批量优化完成 ===")
        print(f"总图像数: {len(matching_files)}")
        print(f"成功优化: {success_count}")
        print(f"失败数量: {len(matching_files) - success_count}")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"平均每张图像: {total_time/len(matching_files):.2f} 秒")
        print(f"输出目录: {self.output_base_path}")


if __name__ == "__main__":
    # 配置参数
    algorithm_name = "CDDFuse_val"  # 可修改的算法名称
    learning_rate = 0.003   # 学习率
    iterations = 200        # 迭代次数
    
    # 创建批量优化器并运行
    optimizer = BatchOptimizer(algorithm_name, learning_rate, iterations)
    optimizer.run_batch_optimization()
