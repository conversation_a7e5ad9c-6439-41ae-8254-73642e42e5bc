from Losses import ssim
from Losses import mi
from Losses import ei
from Losses import psnr
from Losses import en
from Losses import ce
from Losses import ag
from Losses import rmse
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import q_cb
import torchvision.transforms.functional as TF
from PIL import Image

vis = TF.to_tensor(Image.open('input/IR/jeep.bmp')).unsqueeze(0)
ir = TF.to_tensor(Image.open('input/VIS/jeep.bmp')).unsqueeze(0)
# fused = TF.to_tensor(Image.open('input/fused/jeep_ADF.png')).unsqueeze(0) 
fused = TF.to_tensor(Image.open('../vgg-tensorflow-master/images/jeep/mono/FusionGAN/matched_image.png')).unsqueeze(0) 

ag_val = ag.ag_metric(vis,ir,fused)
ssim_val = ssim.ssim_metric(vis,ir,fused)
mi_val = mi.mi_metric(vis,ir,fused)
ei_val = ei.ei_metric(vis,ir,fused)
psnr_val = psnr.psnr_metric(vis,ir,fused)
qabf_val = qabf.q_abf_metric(vis,ir,fused)
ce_val = ce.ce_metric(vis,ir,fused)
en_val = en.en_metric(vis,ir,fused)
sf_val = sf.sf_metric(vis,ir,fused)
rmse_val = rmse.rmse_metric(vis,ir,fused)
sd_val = sd.sd_metric(vis,ir,fused)
qcb_val = q_cb.q_cb_metric(vis,ir,fused)


print(ce_val)
print(en_val)
print(mi_val)
print(psnr_val)
print(ei_val)
print(ag_val)
print(qabf_val)
print(sd_val)
print(sf_val)
print(rmse_val)
print(ssim_val)
print(qcb_val)















