import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import time
import numpy as np
import pandas as pd
from Losses import ssim
from Losses import sf
from Losses import qabf
from Losses import vif
from Losses import scd
from Losses import mi
from Losses import cc
from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric, ei_metric
from tqdm import tqdm

# 锁随机种子
seed = 42
torch.manual_seed(seed)
np.random.seed(seed)

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 用户选择的参数
method = 'CDDFuse'
scene = '74'
metric = 'test'

# 图像路径
vis_path = f'input4table/VIS/{scene}.png'
ir_path = f'input4table/IR/{scene}.png'
fused_path = f'input4table/Fused/{method}/{scene}_{method}.png'

# 设置超参数
lr = 0.003

# 定义要测试的图像尺寸和迭代次数
image_sizes = [128, 256, 512]
iteration_counts = [100, 200, 500, 1000]

# 创建主输出文件夹
main_output_folder = f"./computational_cost/{scene}/{metric}/{method}/"
os.makedirs(main_output_folder, exist_ok=True)

# 图像预处理函数
def resize_image(image_tensor, size):
    """将图像张量调整到指定尺寸"""
    return TF.resize(image_tensor, (size, size), antialias=True)

def read_and_resize_image(path, size):
    """读取图像并调整到指定尺寸"""
    image = TF.to_tensor(Image.open(path)).unsqueeze(0).to(device)
    if image.shape[1] == 3:
        image = image.mean(dim=1, keepdim=True)
    return resize_image(image, size)

# 计算指标绝对值
def calculate_metrics_absolute(vis, ir, fused):
    metrics = {}
    metrics['CE'] = ce_metric(vis, ir, fused).item()
    metrics['EI'] = ei_metric(vis, ir, fused).item()
    metrics['EN'] = en_metric(vis, ir, fused).item()
    metrics['MI'] = mi_metric(vis, ir, fused).item()
    metrics['PSNR'] = psnr_metric(vis, ir, fused).item()
    metrics['Qabf'] = q_abf_metric(vis, ir, fused).item()
    metrics['SD'] = sd_metric(vis, ir, fused).item()
    metrics['SF'] = sf_metric(vis, ir, fused).item()
    metrics['SSIM'] = ssim_metric(vis, ir, fused).item()
    metrics['VIF'] = vif_metric(vis, ir, fused).item()
    metrics['SCD'] = scd_metric(vis, ir, fused).item()
    metrics['CC'] = cc_metric(vis, ir, fused).item()
    return metrics

# 损失函数
def monotone_incresing_loss(x, y, vis, ir):
    # Calculate the difference between the optimized image and the original image for each metric
    ssim_diff = (ssim.ssim_metric(vis, ir, y) - ssim.ssim_metric(vis, ir, x)) / ssim.ssim_metric(vis, ir, x)
    mi_diff = (mi.mi_metric(vis, ir, y) - mi.mi_metric(vis, ir, x)) / mi.mi_metric(vis, ir, x)
    qabf_diff = (qabf.q_abf_metric(vis, ir, y) - qabf.q_abf_metric(vis, ir, x)) / qabf.q_abf_metric(vis, ir, x)
    sf_diff = (sf.sf_metric(vis, ir, y) - sf.sf_metric(vis, ir, x)) / sf.sf_metric(vis, ir, x)
    vif_diff = (vif.vif_metric(vis, ir, y) - vif.vif(vis, ir, x)) / vif.vif_metric(vis, ir, x)
    scd_diff = (scd.scd_metric(vis, ir, y) - scd.scd(vis, ir, x)) / scd.scd_metric(vis, ir, x)
    cc_diff = (cc.cc_metric(vis, ir, y) - cc.cc(vis, ir, x)) / cc.cc_metric(vis, ir, x)

    # Apply the mapping function to the differences
    def mapping_function(diff):
        mapped_diff = (torch.exp(5.5 * diff) - torch.exp(5.5 * -diff))/ (torch.exp(5.5 * diff) + torch.exp(5.5 * -diff))
        return mapped_diff

    # Apply the mapping function to each difference
    ssim_mapped = mapping_function(ssim_diff)
    mi_mapped = mapping_function(mi_diff)
    qabf_mapped = mapping_function(qabf_diff)
    sf_mapped = mapping_function(sf_diff)
    vif_mapped = mapping_function(vif_diff)
    scd_mapped = mapping_function(scd_diff)
    cc_mapped = mapping_function(cc_diff)

    # Calculate the loss
    loss = (
        ssim_mapped +
        mi_mapped * 0.01 +
        qabf_mapped * 0.5 +
        sf_mapped * 0.1 +
        vif_mapped +
        scd_mapped +
        cc_mapped
        ) / 7
    
    return loss

# 优化单个尺寸和迭代次数组合的图像
def optimize_image_size_iterations(size, num_iterations):
    print(f"\n{'='*60}")
    print(f"开始优化 {size}x{size} 尺寸的图像, {num_iterations} 次迭代")
    print(f"{'='*60}")

    # 创建该尺寸和迭代次数的输出文件夹
    size_output_folder = os.path.join(main_output_folder, f"size_{size}x{size}_iter_{num_iterations}/")
    os.makedirs(size_output_folder, exist_ok=True)
    
    # 读取并调整图像尺寸
    vis = read_and_resize_image(vis_path, size)
    ir = read_and_resize_image(ir_path, size)
    fused = read_and_resize_image(fused_path, size)
    
    # 创建优化参数
    optimized_fused = nn.Parameter(fused.clone()).to(device)
    optimizer = optim.Adam([optimized_fused], lr)
    
    # 记录数据
    losses = []
    running_times = []
    metrics_history = []
    milestone_metrics = []
    milestone_iterations = []
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 优化迭代
    print(f"开始优化 {size}x{size} 图像, {num_iterations} 次迭代...")

    # 使用tqdm显示进度条
    pbar = tqdm(range(num_iterations + 1), desc=f"Size {size}x{size}, Iter {num_iterations}")

    for i in pbar:
        iter_start_time = time.time()

        # 计算损失
        current_loss = monotone_incresing_loss(optimized_fused, fused, vis, ir)

        # 反向传播和优化
        optimizer.zero_grad()
        current_loss.backward()
        optimizer.step()

        # 记录时间和损失
        iter_time = time.time() - iter_start_time
        running_times.append(iter_time)
        losses.append(current_loss.item())

        # 计算当前指标
        current_metrics = calculate_metrics_absolute(vis, ir, optimized_fused)
        current_metrics['Iteration'] = i
        current_metrics['Loss'] = current_loss.item()
        current_metrics['Time'] = iter_time
        current_metrics['Size'] = size
        current_metrics['NumIterations'] = num_iterations
        metrics_history.append(current_metrics)

        # 更新进度条描述
        pbar.set_postfix({
            'Loss': f'{current_loss.item():.6f}',
            'Time': f'{iter_time:.3f}s',
            'SSIM': f'{current_metrics["SSIM"]:.4f}'
        })

        # 每100轮保存里程碑
        if i % 100 == 0 or i == num_iterations:
            milestone_image = optimized_fused.detach().clone().clamp(0, 1)
            milestone_image_path = f"{size_output_folder}{scene}_{method}_{size}x{size}_{num_iterations}iter_step_{i}.png"
            TF.to_pil_image(milestone_image.squeeze(0).cpu()).save(milestone_image_path)

            milestone_metrics.append(current_metrics.copy())
            milestone_iterations.append(i)
    
    pbar.close()
    
    # 计算总时间
    total_time = time.time() - total_start_time
    
    # 保存最终优化图像
    final_optimized_image = optimized_fused.detach().clone().clamp(0, 1)
    final_optimized_path = f"{size_output_folder}{scene}_{method}_{size}x{size}_{num_iterations}iter_final.png"
    TF.to_pil_image(final_optimized_image.squeeze(0).cpu()).save(final_optimized_path)

    # 计算最终指标对比
    original_metrics = calculate_metrics_absolute(vis, ir, fused)
    final_metrics = calculate_metrics_absolute(vis, ir, optimized_fused)

    # 保存详细数据
    history_df = pd.DataFrame(metrics_history)
    history_excel_path = f"{size_output_folder}{scene}_{method}_{size}x{size}_{num_iterations}iter_history.xlsx"
    history_df.to_excel(history_excel_path, index=False)

    milestone_df = pd.DataFrame(milestone_metrics)
    milestone_excel_path = f"{size_output_folder}{scene}_{method}_{size}x{size}_{num_iterations}iter_milestones.xlsx"
    milestone_df.to_excel(milestone_excel_path, index=False)

    # 返回汇总结果
    result_summary = {
        'size': size,
        'num_iterations': num_iterations,
        'total_time': total_time,
        'avg_time_per_iter': np.mean(running_times),
        'final_loss': losses[-1],
        'original_metrics': original_metrics,
        'final_metrics': final_metrics,
        'metrics_history': metrics_history,
        'milestone_metrics': milestone_metrics,
        'milestone_iterations': milestone_iterations,
        'losses': losses,
        'running_times': running_times
    }

    print(f"\n{size}x{size} ({num_iterations} 次迭代) 优化完成!")
    print(f"总时间: {total_time:.2f}s")
    print(f"平均每次迭代时间: {np.mean(running_times):.4f}s")
    print(f"最终损失: {losses[-1]:.6f}")
    print(f"最终SSIM: {final_metrics['SSIM']:.4f}")

    return result_summary

# 主程序
def main():
    print("开始定量分析...")
    print(f"Method: {method}, Scene: {scene}, Metric: {metric}")
    print(f"测试尺寸: {image_sizes}")
    print(f"迭代次数: {iteration_counts}")
    print(f"学习率: {lr}")

    # 存储所有尺寸和迭代次数组合的结果
    all_results = []

    # 对每个尺寸和每个迭代次数进行优化
    for size in image_sizes:
        for num_iterations in iteration_counts:
            print(f"\n{'='*80}")
            print(f"开始测试: {size}x{size} 图像, {num_iterations} 次迭代")
            print(f"{'='*80}")

            result = optimize_image_size_iterations(size, num_iterations)
            all_results.append(result)

    # 生成综合分析报告
    print("\n" + "="*80)
    print("生成综合分析报告...")
    print("="*80)

    # 创建综合分析数据
    analysis_data = []
    for result in all_results:
        size = result['size']
        num_iter = result['num_iterations']
        analysis_data.append({
            'Size': f"{size}x{size}",
            'Iterations': num_iter,
            'Total_Time(s)': result['total_time'],
            'Avg_Time_Per_Iter(s)': result['avg_time_per_iter'],
            'Final_Loss': result['final_loss'],
            'Original_SSIM': result['original_metrics']['SSIM'],
            'Final_SSIM': result['final_metrics']['SSIM'],
            'SSIM_Improvement': result['final_metrics']['SSIM'] - result['original_metrics']['SSIM'],
            'Original_MI': result['original_metrics']['MI'],
            'Final_MI': result['final_metrics']['MI'],
            'MI_Improvement': result['final_metrics']['MI'] - result['original_metrics']['MI'],
            'Original_Qabf': result['original_metrics']['Qabf'],
            'Final_Qabf': result['final_metrics']['Qabf'],
            'Qabf_Improvement': result['final_metrics']['Qabf'] - result['original_metrics']['Qabf'],
            'Pixels': size * size,
            'Time_Per_Pixel': result['total_time'] / (size * size)
        })

    # 保存综合分析数据
    analysis_df = pd.DataFrame(analysis_data)
    analysis_excel_path = f"{main_output_folder}comprehensive_analysis.xlsx"
    analysis_df.to_excel(analysis_excel_path, index=False)
    print(f"综合分析数据已保存到: {analysis_excel_path}")

    # 创建可视化图表
    create_analysis_charts(all_results, main_output_folder)

    # 打印汇总报告
    print_summary_report(all_results)

    return all_results

def create_analysis_charts(results, output_folder):
    """创建分析图表"""
    print("创建分析图表...")

    # 设置matplotlib参数
    plt.rcParams.update({'font.size': 14})

    # 1. 运行时间对比图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle(f'Quantitative Analysis - {method} {scene}', fontsize=18, fontweight='bold')

    # 准备数据 - 按尺寸和迭代次数组织
    size_iter_combinations = [(r['size'], r['num_iterations']) for r in results]
    total_times = [r['total_time'] for r in results]
    avg_times = [r['avg_time_per_iter'] for r in results]
    final_losses = [r['final_loss'] for r in results]

    # 创建标签
    labels = [f"{size}x{size}\n{iter}iter" for size, iter in size_iter_combinations]

    # 生成颜色
    colors = plt.cm.Set3(np.linspace(0, 1, len(results)))

    # 总运行时间对比
    ax1.bar(range(len(results)), total_times, color=colors)
    ax1.set_xlabel('Size x Iterations')
    ax1.set_ylabel('Total Time (seconds)')
    ax1.set_title('Total Optimization Time', fontweight='bold')
    ax1.set_xticks(range(len(results)))
    ax1.set_xticklabels(labels, rotation=45, ha='right')

    # 在柱状图上添加数值标签
    for i, v in enumerate(total_times):
        ax1.text(i, v + max(total_times)*0.01, f'{v:.1f}s', ha='center', va='bottom', fontweight='bold', fontsize=10)

    # 平均每次迭代时间对比
    ax2.bar(range(len(results)), avg_times, color=colors)
    ax2.set_xlabel('Size x Iterations')
    ax2.set_ylabel('Average Time per Iteration (seconds)')
    ax2.set_title('Average Time per Iteration', fontweight='bold')
    ax2.set_xticks(range(len(results)))
    ax2.set_xticklabels(labels, rotation=45, ha='right')

    # 添加数值标签
    for i, t in enumerate(avg_times):
        ax2.text(i, t + max(avg_times)*0.01, f'{t:.4f}s', ha='center', va='bottom', fontweight='bold', fontsize=10)

    # 最终损失对比
    ax3.bar(range(len(results)), final_losses, color=colors)
    ax3.set_xlabel('Size x Iterations')
    ax3.set_ylabel('Final Loss')
    ax3.set_title('Final Loss Comparison', fontweight='bold')
    ax3.set_xticks(range(len(results)))
    ax3.set_xticklabels(labels, rotation=45, ha='right')

    # 在柱状图上添加数值标签
    for i, v in enumerate(final_losses):
        ax3.text(i, v + max(final_losses)*0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

    # 时间复杂度分析 (时间 vs 像素数 x 迭代次数)
    complexity_factor = [r['size']*r['size']*r['num_iterations'] for r in results]
    ax4.scatter(complexity_factor, total_times, s=100, c=colors, alpha=0.7)
    ax4.set_xlabel('Pixels × Iterations')
    ax4.set_ylabel('Total Time (seconds)')
    ax4.set_title('Time Complexity Analysis', fontweight='bold')

    # 添加标签
    for i, (cf, tt) in enumerate(zip(complexity_factor, total_times)):
        size, iter_count = size_iter_combinations[i]
        ax4.annotate(f'{size}x{size}\n{iter_count}iter', (cf, tt), textcoords="offset points",
                    xytext=(5,5), ha='left', fontweight='bold', fontsize=9)

    plt.tight_layout()
    chart_path = os.path.join(output_folder, 'quantitative_analysis_overview.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"综合分析图表已保存到: {chart_path}")

    # 2. 指标改善对比图
    create_metrics_improvement_chart(results, output_folder)

    # 3. 损失收敛曲线对比
    create_loss_convergence_chart(results, output_folder)

def create_metrics_improvement_chart(results, output_folder):
    """创建指标改善对比图"""
    fig, axes = plt.subplots(2, 3, figsize=(20, 14))
    fig.suptitle(f'Metrics Improvement Comparison - {method} {scene}', fontsize=18, fontweight='bold')

    # 准备数据
    size_iter_combinations = [(r['size'], r['num_iterations']) for r in results]
    labels = [f"{size}x{size}\n{iter}iter" for size, iter in size_iter_combinations]

    metrics_to_plot = ['SSIM', 'MI', 'Qabf', 'PSNR', 'VIF', 'CC']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']

    axes_flat = axes.flatten()

    for idx, metric in enumerate(metrics_to_plot):
        if idx >= len(axes_flat):
            break

        ax = axes_flat[idx]

        original_values = [r['original_metrics'][metric] for r in results]
        final_values = [r['final_metrics'][metric] for r in results]
        improvements = [f - o for f, o in zip(final_values, original_values)]

        x = np.arange(len(results))
        width = 0.35

        ax.bar(x - width/2, original_values, width, label='Original', color=colors[idx], alpha=0.7)
        ax.bar(x + width/2, final_values, width, label='Optimized', color=colors[idx])

        ax.set_xlabel('Size x Iterations')
        ax.set_ylabel(metric)
        ax.set_title(f'{metric} Comparison', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(labels, rotation=45, ha='right')
        ax.legend()

        # 添加改善值标签
        for i, imp in enumerate(improvements):
            max_val = max(final_values[i], original_values[i])
            y_offset = max(max(final_values), max(original_values)) * 0.02
            ax.text(i, max_val + y_offset,
                   f'+{imp:.4f}' if imp >= 0 else f'{imp:.4f}',
                   ha='center', va='bottom', fontweight='bold', fontsize=9,
                   color='green' if imp >= 0 else 'red')

    plt.tight_layout()
    metrics_chart_path = os.path.join(output_folder, 'metrics_improvement_comparison.png')
    plt.savefig(metrics_chart_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"指标改善对比图已保存到: {metrics_chart_path}")

def create_loss_convergence_chart(results, output_folder):
    """创建损失收敛曲线对比图"""
    # 按尺寸分组创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'Loss Convergence Comparison - {method} {scene}', fontsize=16, fontweight='bold')

    # 按尺寸分组
    size_groups = {}
    for result in results:
        size = result['size']
        if size not in size_groups:
            size_groups[size] = []
        size_groups[size].append(result)

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    for idx, (size, size_results) in enumerate(size_groups.items()):
        if idx >= len(axes):
            break

        ax = axes[idx]

        for i, result in enumerate(size_results):
            losses = result['losses']
            num_iter = result['num_iterations']
            iterations = range(len(losses))

            ax.plot(iterations, losses, color=colors[i], linewidth=2,
                   label=f'{num_iter} iter (Final: {losses[-1]:.4f})', alpha=0.8)

        ax.set_xlabel('Iteration')
        ax.set_ylabel('Loss')
        ax.set_title(f'{size}x{size} Image Size', fontweight='bold')
        ax.legend(fontsize=10)
        ax.set_yscale('log')  # 使用对数刻度更好地显示收敛过程

    plt.tight_layout()
    loss_chart_path = os.path.join(output_folder, 'loss_convergence_comparison.png')
    plt.savefig(loss_chart_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"损失收敛对比图已保存到: {loss_chart_path}")

def print_summary_report(results):
    """打印汇总报告"""
    print("\n" + "="*80)
    print("定量分析汇总报告")
    print("="*80)

    print(f"Method: {method}")
    print(f"Scene: {scene}")
    print(f"Learning Rate: {lr}")
    print(f"迭代次数组合: {iteration_counts}")
    print(f"测试尺寸: {image_sizes}")

    print(f"\n{'Size':<12} {'Iterations':<12} {'Total Time':<12} {'Avg Time/Iter':<15} {'Final Loss':<12} {'SSIM Imp.':<12}")
    print("-" * 95)

    for result in results:
        size = result['size']
        num_iter = result['num_iterations']
        total_time = result['total_time']
        avg_time = result['avg_time_per_iter']
        final_loss = result['final_loss']
        ssim_imp = result['final_metrics']['SSIM'] - result['original_metrics']['SSIM']

        print(f"{size}x{size:<8} {num_iter:<12} {total_time:<12.2f} {avg_time:<15.6f} {final_loss:<12.6f} {ssim_imp:<12.6f}")

    # 计算时间复杂度分析
    print(f"\n时间复杂度分析:")

    # 按尺寸分组分析
    size_groups = {}
    for result in results:
        size = result['size']
        if size not in size_groups:
            size_groups[size] = []
        size_groups[size].append(result)

    # 分析每个尺寸下迭代次数与时间的关系
    for size, size_results in size_groups.items():
        print(f"\n{size}x{size} 尺寸分析:")
        size_results.sort(key=lambda x: x['num_iterations'])

        for i, result in enumerate(size_results):
            iter_count = result['num_iterations']
            total_time = result['total_time']
            avg_time = result['avg_time_per_iter']
            print(f"  {iter_count:4d} 次迭代: 总时间 {total_time:7.2f}s, 平均 {avg_time:.6f}s/iter")

        # 计算时间与迭代次数的线性关系
        if len(size_results) >= 2:
            time_per_iter_ratios = []
            for i in range(1, len(size_results)):
                prev_result = size_results[i-1]
                curr_result = size_results[i]

                iter_ratio = curr_result['num_iterations'] / prev_result['num_iterations']
                time_ratio = curr_result['total_time'] / prev_result['total_time']

                time_per_iter_ratios.append(time_ratio / iter_ratio)
                print(f"  {prev_result['num_iterations']} -> {curr_result['num_iterations']} 次迭代: "
                      f"时间比 {time_ratio:.2f}, 理论比 {iter_ratio:.2f}, "
                      f"效率比 {time_ratio/iter_ratio:.3f}")

            avg_efficiency = np.mean(time_per_iter_ratios)
            if avg_efficiency < 1.1:
                efficiency_desc = "线性扩展 (优秀)"
            elif avg_efficiency < 1.3:
                efficiency_desc = "接近线性 (良好)"
            else:
                efficiency_desc = "超线性增长 (需优化)"

            print(f"  平均效率比: {avg_efficiency:.3f} - {efficiency_desc}")

    # 分析不同尺寸间的时间复杂度
    print(f"\n尺寸间复杂度分析:")
    sizes = sorted(list(size_groups.keys()))

    for i in range(len(sizes)-1):
        size1, size2 = sizes[i], sizes[i+1]

        # 找到相同迭代次数的结果进行比较
        for iter_count in iteration_counts:
            result1 = next((r for r in size_groups[size1] if r['num_iterations'] == iter_count), None)
            result2 = next((r for r in size_groups[size2] if r['num_iterations'] == iter_count), None)

            if result1 and result2:
                pixel_ratio = (size2 * size2) / (size1 * size1)
                time_ratio = result2['total_time'] / result1['total_time']

                print(f"  {size1}x{size1} -> {size2}x{size2} ({iter_count} 次迭代): "
                      f"像素比 {pixel_ratio:.1f}, 时间比 {time_ratio:.2f}")

                if time_ratio / pixel_ratio < 1.2:
                    complexity_desc = "接近线性复杂度"
                elif time_ratio / pixel_ratio < 2.0:
                    complexity_desc = "介于线性和平方之间"
                else:
                    complexity_desc = "接近平方复杂度"

                print(f"    复杂度估计: {complexity_desc} (比值 {time_ratio/pixel_ratio:.2f})")
                break

if __name__ == "__main__":
    results = main()
