#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标可视化脚本
从不同学习率文件夹中提取milestone_metrics数据并绘制归一化的指标变化曲线
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import glob
from pathlib import Path
import re

class MetricsVisualizer:
    def __init__(self, base_path):
        self.base_path = base_path
        self.lr_folders = []
        self.all_data = {}
        
        # 定义指标列表（排除Iteration和Loss）
        self.metrics = ['CE', 'EN', 'MI', 'PSNR', 'Qabf', 'SD', 'SF', 'SSIM', 'VIF', 'SCD', 'CC']
        
        # 定义颜色映射 - 使用高对比度颜色便于区分
        self.colors = {
            'CE': '#FF0000',      # 红色
            'EN': '#00AA00',      # 绿色
            'MI': '#0066FF',      # 蓝色
            'PSNR': '#FF8800',    # 橙色
            'Qabf': '#AA00AA',    # 紫色
            'SD': '#00AAAA',      # 青色
            'SF': '#AAAA00',      # 黄色
            'SSIM': '#FF1493',    # 深粉色
            'VIF': '#32CD32',     # 酸橙绿
            'SCD': '#8B4513',     # 马鞍棕
            'CC': '#4169E1'       # 皇家蓝
        }
        

    
    def find_lr_folders(self):
        """查找所有lr开头的文件夹"""
        pattern = os.path.join(self.base_path, "lr*")
        lr_paths = glob.glob(pattern)
        
        for path in lr_paths:
            if os.path.isdir(path):
                folder_name = os.path.basename(path)
                # 提取学习率数值
                lr_match = re.search(r'lr([\d.]+)', folder_name)
                if lr_match:
                    lr_value = lr_match.group(1)
                    self.lr_folders.append({
                        'path': path,
                        'lr_value': lr_value,
                        'folder_name': folder_name
                    })
        
        # 按学习率数值排序
        self.lr_folders.sort(key=lambda x: float(x['lr_value']))
        print(f"找到 {len(self.lr_folders)} 个学习率文件夹:")
        for folder in self.lr_folders:
            print(f"  - {folder['folder_name']} (lr={folder['lr_value']})")
    
    def load_data_from_folder(self, lr_folder):
        """从指定的lr文件夹中加载数据"""
        excel_path = os.path.join(
            lr_folder['path'], 
            'Res2Fusion', 
            '33_Res2Fusion_milestone_metrics.xlsx'
        )
        
        if not os.path.exists(excel_path):
            print(f"警告: 文件不存在 {excel_path}")
            return None
        
        try:
            df = pd.read_excel(excel_path)
            print(f"成功加载数据: {lr_folder['folder_name']}, 数据行数: {len(df)}")
            return df
        except Exception as e:
            print(f"错误: 无法读取文件 {excel_path}: {e}")
            return None
    
    def normalize_metrics(self, data, target_range=(0, 10)):
        """对指标进行归一化处理到指定范围"""
        normalized_data = data.copy()
        
        for metric in self.metrics:
            if metric in data.columns:
                values = data[metric].values
                min_val = np.min(values)
                max_val = np.max(values)
                
                if max_val > min_val:  # 避免除零
                    # 归一化到0-1，然后缩放到目标范围
                    normalized_values = (values - min_val) / (max_val - min_val)
                    normalized_values = normalized_values * (target_range[1] - target_range[0]) + target_range[0]
                    normalized_data[metric] = normalized_values
                else:
                    # 如果所有值相同，设为范围中点
                    normalized_data[metric] = (target_range[0] + target_range[1]) / 2
                
                print(f"  {metric}: [{min_val:.4f}, {max_val:.4f}] -> [{target_range[0]}, {target_range[1]}]")
        
        return normalized_data
    
    def load_all_data(self):
        """加载所有lr文件夹的数据"""
        for lr_folder in self.lr_folders:
            print(f"\n处理文件夹: {lr_folder['folder_name']}")
            
            data = self.load_data_from_folder(lr_folder)
            if data is not None:
                # 过滤iteration 0-2000的数据
                filtered_data = data[(data['Iteration'] >= 0) & (data['Iteration'] <= 2000)]
                
                if len(filtered_data) > 0:
                    print(f"  过滤后数据行数: {len(filtered_data)} (Iteration 0-2000)")
                    print("  归一化指标:")
                    normalized_data = self.normalize_metrics(filtered_data)
                    
                    self.all_data[lr_folder['lr_value']] = {
                        'data': normalized_data,
                        'lr_folder': lr_folder
                    }
                else:
                    print(f"  警告: 没有找到Iteration 0-2000范围内的数据")
    
    def plot_single_lr(self, lr_value, data, ax):
        """绘制单个学习率的指标曲线"""
        iterations = data['Iteration']
        
        for i, metric in enumerate(self.metrics):
            if metric in data.columns:
                values = data[metric]
                ax.plot(iterations, values,
                       color=self.colors[metric],
                       linestyle='-',
                       linewidth=2,
                       marker='o',
                       markersize=3,
                       label=metric,
                       alpha=0.8)
        
        ax.set_title(f'η={lr_value}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Iteration', fontsize=12)
        ax.set_ylabel('Normalized Metric Value', fontsize=12)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
        ax.set_xlim(0, 1000)
        ax.set_ylim(0, 10)
    
    def create_visualization(self):
        """创建完整的可视化图表"""
        if not self.all_data:
            print("错误: 没有可用的数据进行可视化")
            return
        
        n_plots = len(self.all_data)
        
        # 计算子图布局
        if n_plots <= 2:
            rows, cols = 1, n_plots
            figsize = (12 * cols, 8)
        elif n_plots <= 4:
            rows, cols = 2, 2
            figsize = (20, 12)
        elif n_plots <= 6:
            rows, cols = 2, 3
            figsize = (24, 12)
        else:
            rows, cols = 3, (n_plots + 2) // 3
            figsize = (8 * cols, 6 * rows)
        
        fig, axes = plt.subplots(rows, cols, figsize=figsize)
        
        # 如果只有一个子图，确保axes是数组
        if n_plots == 1:
            axes = [axes]
        elif rows == 1 or cols == 1:
            axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]
        else:
            axes = axes.flatten()
        
        # 绘制每个学习率的图表
        for i, (lr_value, data_info) in enumerate(self.all_data.items()):
            if i < len(axes):
                self.plot_single_lr(lr_value, data_info['data'], axes[i])
        
        # 隐藏多余的子图
        for i in range(len(self.all_data), len(axes)):
            axes[i].set_visible(False)
        
        plt.suptitle('', 
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(self.base_path, 'metrics_comparison_all_lr.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"\n可视化图表已保存到: {output_path}")
        
        plt.show()
    
    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.all_data:
            return
        
        print("\n=== 数据汇总报告 ===")
        
        summary_data = []
        for lr_value, data_info in self.all_data.items():
            data = data_info['data']
            
            # 计算每个指标的起始值和最终值
            row = {'Learning_Rate': lr_value}
            
            for metric in self.metrics:
                if metric in data.columns:
                    initial_val = data[metric].iloc[0] if len(data) > 0 else 0
                    final_val = data[metric].iloc[-1] if len(data) > 0 else 0
                    improvement = final_val - initial_val
                    
                    row[f'{metric}_Initial'] = initial_val
                    row[f'{metric}_Final'] = final_val
                    row[f'{metric}_Improvement'] = improvement
            
            summary_data.append(row)
        
        # 保存汇总数据
        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(self.base_path, 'metrics_summary_report.xlsx')
        summary_df.to_excel(summary_path, index=False)
        print(f"汇总报告已保存到: {summary_path}")
    
    def run(self):
        """运行完整的分析流程"""
        print("=== 指标可视化分析开始 ===")
        print(f"基础路径: {self.base_path}")
        
        # 1. 查找lr文件夹
        self.find_lr_folders()
        
        if not self.lr_folders:
            print("错误: 没有找到任何lr开头的文件夹")
            return
        
        # 2. 加载所有数据
        print("\n=== 加载数据 ===")
        self.load_all_data()
        
        if not self.all_data:
            print("错误: 没有成功加载任何数据")
            return
        
        # 3. 创建可视化
        print("\n=== 创建可视化 ===")
        self.create_visualization()
        
        # 4. 生成汇总报告
        print("\n=== 生成汇总报告 ===")
        self.generate_summary_report()
        
        print("\n=== 分析完成 ===")


if __name__ == "__main__":
    # 设置基础路径
    base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\output\33"
    
    # 创建可视化器并运行
    visualizer = MetricsVisualizer(base_path)
    visualizer.run()
