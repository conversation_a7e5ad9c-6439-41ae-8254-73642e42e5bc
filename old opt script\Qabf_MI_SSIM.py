import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms.functional as TF
import os
import imageio
import numpy as np
from Losses import qabf
from Losses import mi
from Losses import ssim
import kornia



os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'


class ImageFusionOptimizer:
    def __init__(self, method, scene, metric, vis_path, ir_path, fused_path, output_folder, display_interval=100):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.method = method
        self.scene = scene
        self.metric = metric
        self.vis_path = vis_path
        self.ir_path = ir_path
        self.fused_path = fused_path
        self.output_folder = output_folder
        self.display_interval = display_interval
        self.lr = lr
        self.num_iterations = num_iterations
        
        self.transform = transforms.Compose([transforms.ToTensor()])

        self.vis = self._process_image(Image.open(self.vis_path))
        self.ir = self._process_image(Image.open(self.ir_path))
        self.fused = self._process_image(Image.open(self.fused_path))

        self._check_channel_dimensions()

        self.optimized_fused = nn.Parameter(self.fused.clone())
        self.optimizer = optim.Adam([self.optimized_fused], self.lr)

        self.loss_weights = {'mi': 0.05 , 'qabf': 1.0, 'ssim':1.0}



        # 设置图像窗口的大小
        self.fig = plt.figure(figsize=(6, 4), dpi=300)

        # 两个子图的初始状态
        self.original_subplt = plt.subplot(121)
        self.optimized_subplt = plt.subplot(122)

        self.losses_mi,  self.losses_qabf, self.losses_combine, self.losses_ssim = [], [], [], []
        self.mi_val = []

    def _process_image(self, image):
        image_tensor = TF.to_tensor(image).unsqueeze(0).to(self.device)
        if image_tensor.shape[1] == 3:
            image_tensor = image_tensor.mean(dim=1, keepdim=True)
        return image_tensor

    def _check_channel_dimensions(self):
        if self.vis.shape[1] != 1 or self.ir.shape[1] != 1 or self.fused.shape[1] != 1:
            raise ValueError("Input images must have a single channel.")

    def mi_loss(self, x, y, z ):
        vis = mi.mi(x,z)
        ir = mi.mi(y,z)
        mi_total_loss = (vis + ir) / 2
        return -mi_total_loss

    def ssim_loss(self, x, y, z):
        vis = ssim.ssim_approach_loss(x,z, window_size=11)
        ir = ssim.ssim_approach_loss(y,z, window_size=11)
        ssim_total_loss = (vis + ir) / 2
        return ssim_total_loss

    def qabf_loss(self, x,y,z):
        return -qabf.q_abf(x,y,z)
    

    def calculate_losses(self):

        current_mi_loss = self.mi_loss(self.vis, self.ir, self.optimized_fused)

        current_ssim_loss = self.ssim_loss(self.vis, self.ir, self.optimized_fused)

        current_qabf_loss = self.qabf_loss(self.vis, self.ir, self.optimized_fused)
     
        combine_loss = (self.loss_weights['mi']    * current_mi_loss +
                        self.loss_weights['qabf']  * current_qabf_loss +
                        self.loss_weights['ssim']  * current_ssim_loss 
                        ) / (self.loss_weights['mi'] + self.loss_weights['qabf'] + self.loss_weights['ssim'])

        return current_mi_loss, current_qabf_loss, combine_loss, current_ssim_loss

    def train(self):
        # Initialize the video writer
        video_path = f'{self.method}_{self.scene}_{self.metric}.mp4'
        writer = imageio.get_writer(video_path, fps=10)

        for i in range(self.num_iterations):
            mi_loss, qabf_loss, combine_loss, ssim_loss = self.calculate_losses()

            self.optimizer.zero_grad()
            combine_loss.backward()
            self.optimizer.step()

            self.losses_mi.append(mi_loss.item())
            self.losses_ssim.append(ssim_loss.item())
            self.losses_qabf.append(qabf_loss.item())
            
            self.losses_combine.append(combine_loss.item())

            if i % self.display_interval == 0:
                print(f"Iteration {i + 100}")
                print(f"mi_loss: {mi_loss.item()}")
                print(f"ssim_loss: {ssim_loss.item()}")
                print(f"qabf_loss: {qabf_loss.item()}")
                print(f"Combine_loss: {combine_loss.item()}")

                optimized_image = self.optimized_fused.detach().clone().clamp(0, 1)
                optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
                optimized_image_display = optimized_image_display.astype(np.uint8)
                optimized_image_path = f"{self.output_folder}{self.method}_{self.scene}_{self.metric}_{i + 100}.png"

                # 创建文件夹（如果不存在）
                os.makedirs(self.output_folder, exist_ok=True)

                optimized_pil_image = TF.to_pil_image(optimized_image.squeeze(0).cpu())
                optimized_pil_image.save(optimized_image_path)

                # 显示图像
                self.original_subplt.clear()
                self.optimized_subplt.clear()

                self.original_subplt.imshow(self.fused.squeeze(0).cpu().numpy()[0], cmap='gray')
                self.optimized_subplt.imshow(optimized_image_display, cmap='gray')

                # 设置标题和关闭坐标轴
                self.original_subplt.set_title("Original Fused Image")
                self.optimized_subplt.set_title(f"Iteration: {i + 100}")
                self.original_subplt.axis('off')
                self.optimized_subplt.axis('off')

                # 刷新图像
                plt.pause(0.1)

                # 添加图像到视频
                writer.append_data(plt.imread(optimized_image_path))

        # 关闭图像窗口
        plt.close(self.fig)

        # 关闭视频写入器
        writer.close()
        print(f"Video saved to {video_path}")

    def plot_losses(self):
        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_mi, label=f'mi Loss')
        plt.title(f'mi Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'mi Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_mi.png'))
        plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_qabf, label=f'Qabf Loss')
        plt.title(f'Qabf Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'Qabf Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_qabf.png'))
        plt.close
        
        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_ssim, label=f'ssim Loss')
        plt.title(f'ssim Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'ssim Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_ssim.png'))
        plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_combine, label=f'Combine Loss')
        plt.title(f'Combine Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'Combine Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_combine.png'))
        plt.close


        # plt.figure(figsize=(8, 5))
        # plt.plot(self.mi_val, label=f'{self.metric} Value')
        # plt.title(f'{self.metric} Value')
        # plt.xlabel('Iteration')
        # plt.ylabel(f'{self.metric} Value')
        # plt.legend()

        plt.show()


# 使用 ImageFusionOptimizer 类
method = 'CDDFuse'
lr = 0.001
num_iterations = 5000
scene = 'jeep'
metric = 'Combine'
vis_path = f'input/VIS/{scene}.bmp'
ir_path = f'input/IR/{scene}.bmp'
fused_path = f'input/Fused/{scene}_{method}.png'
output_folder = f"../vgg-tensorflow-master/images/{scene}/{metric}/{method}1/"

fusion_optimizer = ImageFusionOptimizer(method, scene, metric, vis_path, ir_path, fused_path, output_folder)
fusion_optimizer.train()
fusion_optimizer.plot_losses()
