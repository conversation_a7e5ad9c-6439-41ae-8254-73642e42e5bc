#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机文件选择器
在指定路径下随机保留50个文件，确保IR和VIS路径下的文件以及对应的标签文件保持一致
"""

import os
import random
import shutil
from pathlib import Path
import argparse


def get_file_stem(filepath):
    """获取文件名（不包含扩展名）"""
    return Path(filepath).stem


def list_files_by_stem(directory):
    """列出目录中所有文件，并按文件名（不含扩展名）分组"""
    if not os.path.exists(directory):
        print(f"警告: 目录不存在: {directory}")
        return {}
    
    files_by_stem = {}
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            stem = get_file_stem(filename)
            if stem not in files_by_stem:
                files_by_stem[stem] = []
            files_by_stem[stem].append(filename)
    
    return files_by_stem


def find_common_stems(directories):
    """找到所有目录中共同存在的文件名（不含扩展名）"""
    all_stems = []
    
    for directory in directories:
        files_by_stem = list_files_by_stem(directory)
        stems = set(files_by_stem.keys())
        all_stems.append(stems)
    
    # 找到所有目录中都存在的文件名
    if all_stems:
        common_stems = all_stems[0]
        for stems in all_stems[1:]:
            common_stems = common_stems.intersection(stems)
        return list(common_stems)
    else:
        return []


def backup_directory(directory, backup_suffix="_backup"):
    """备份目录"""
    if not os.path.exists(directory):
        return None
    
    backup_dir = directory + backup_suffix
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    shutil.copytree(directory, backup_dir)
    print(f"已备份目录: {directory} -> {backup_dir}")
    return backup_dir


def remove_files_except_selected(directory, selected_stems):
    """删除目录中除了选中文件名之外的所有文件"""
    if not os.path.exists(directory):
        print(f"警告: 目录不存在: {directory}")
        return
    
    removed_count = 0
    kept_count = 0
    
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            stem = get_file_stem(filename)
            if stem not in selected_stems:
                os.remove(filepath)
                removed_count += 1
            else:
                kept_count += 1
    
    print(f"目录 {directory}: 保留 {kept_count} 个文件，删除 {removed_count} 个文件")


def main():
    # 定义路径
    base_path = r"F:\.Infrared and Visiable Light Fusion\M3FD4detect\val"
    
    directories = {
        "IR_images": os.path.join(base_path, "IR", "images", "val"),
        "VIS_images": os.path.join(base_path, "VIS", "images", "val"),
        "IR_labels": os.path.join(base_path, "IR", "labels", "val"),
        "VIS_labels": os.path.join(base_path, "VIS", "labels", "val")
    }
    
    print("=== 随机文件选择器 ===")
    print("目标路径:")
    for name, path in directories.items():
        print(f"  {name}: {path}")
    
    # 检查所有目录是否存在
    missing_dirs = []
    for name, path in directories.items():
        if not os.path.exists(path):
            missing_dirs.append(f"{name}: {path}")
    
    if missing_dirs:
        print("\n错误: 以下目录不存在:")
        for missing in missing_dirs:
            print(f"  {missing}")
        return
    
    # 找到所有目录中共同存在的文件名
    print("\n正在分析文件...")
    common_stems = find_common_stems(list(directories.values()))
    
    if not common_stems:
        print("错误: 没有找到所有目录中都存在的共同文件")
        return
    
    print(f"找到 {len(common_stems)} 个共同的文件名")
    
    # 检查是否有足够的文件
    if len(common_stems) < 50:
        print(f"警告: 共同文件数量 ({len(common_stems)}) 少于50个")
        num_to_keep = len(common_stems)
    else:
        num_to_keep = 50
    
    # 随机选择文件
    selected_stems = random.sample(common_stems, num_to_keep)
    print(f"随机选择了 {len(selected_stems)} 个文件")
    
    # 确认操作
    print(f"\n即将在以下目录中保留 {num_to_keep} 个文件，删除其余文件:")
    for name, path in directories.items():
        print(f"  {name}: {path}")
    
    confirm = input("\n是否继续? (y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 备份所有目录
    print("\n正在备份目录...")
    backup_dirs = {}
    for name, path in directories.items():
        backup_dir = backup_directory(path)
        if backup_dir:
            backup_dirs[name] = backup_dir
    
    # 删除非选中的文件
    print("\n正在处理文件...")
    for name, path in directories.items():
        remove_files_except_selected(path, set(selected_stems))
    
    print(f"\n操作完成! 每个目录中保留了 {num_to_keep} 个文件")
    print("\n选中的文件名 (不含扩展名):")
    for i, stem in enumerate(sorted(selected_stems), 1):
        print(f"  {i:2d}. {stem}")
    
    if backup_dirs:
        print("\n备份目录:")
        for name, backup_path in backup_dirs.items():
            print(f"  {name}: {backup_path}")


if __name__ == "__main__":
    # 设置随机种子以便复现结果（可选）
    # random.seed(42)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
    except Exception as e:
        print(f"\n错误: {e}")
        import traceback
        traceback.print_exc()
