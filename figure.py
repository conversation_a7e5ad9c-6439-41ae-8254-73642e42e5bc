import matplotlib.pyplot as plt
import numpy as np

# 学术配色方案 (ColorBrewer 学术调色板)
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
          '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

# 按使用频率排序的数据
metrics = ['EN', 'Qabf', 'VIF', 'SF', 'MI', 'SSIM', 'SCD', 'CC', 'AG', 'PSNR']
frequency = [12, 14, 10, 8, 10, 6, 4, 2, 2, 6]  # 调整为您实际的频率数据

# 创建专业图表
plt.figure(figsize=(10, 6), dpi=300)
ax = plt.gca()

# 水平条形图
y_pos = np.arange(len(metrics))
bars = ax.barh(y_pos, frequency, color=colors, edgecolor='black', linewidth=0.7, zorder=3)

# 添加数值标签
for i, v in enumerate(frequency):
    ax.text(v + 0.2, i, str(v), color='black', va='center', fontsize=9)

# 学术风格设置
plt.xticks(fontname='Times New Roman', fontsize=10)
plt.yticks(y_pos, metrics, fontname='Times New Roman', fontsize=11)
plt.xlabel('Usage Frequency (%)', fontname='Times New Roman', fontsize=12, labelpad=10)
plt.title('Frequency Distribution of Image Fusion Metrics', 
          fontname='Times New Roman', fontsize=13, pad=15)

# 网格线和轴线优化
ax.xaxis.grid(True, linestyle='--', alpha=0.7, zorder=1)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_linewidth(0.5)
ax.spines['bottom'].set_linewidth(0.5)

# 添加图例说明颜色类别
categories = ['Information Theory', 'Feature-based', 'Structural', 'Gradient-based', 'Spectral']
handles = [plt.Rectangle((0,0),1,1, color=colors[i]) for i in range(5)]
plt.legend(handles, categories, loc='lower right', frameon=False, prop={'family': 'Times New Roman'})

plt.tight_layout()
plt.savefig('fusion_metrics_frequency.png', format='png', bbox_inches='tight')
plt.show()