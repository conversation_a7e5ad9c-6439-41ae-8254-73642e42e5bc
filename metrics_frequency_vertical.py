#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标使用频率垂直条形图
生成适用于SCI论文的专业配图 - 大字体版本
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib import rcParams
import pandas as pd

# 设置matplotlib参数以获得高质量图形 - 增大字体
rcParams['font.family'] = 'serif'
rcParams['font.serif'] = ['Times New Roman']
rcParams['font.size'] = 18  # 增大基础字体
rcParams['axes.linewidth'] = 1.5
rcParams['axes.spines.top'] = False
rcParams['axes.spines.right'] = False
rcParams['xtick.direction'] = 'in'
rcParams['ytick.direction'] = 'in'
rcParams['xtick.major.size'] = 8
rcParams['ytick.major.size'] = 8
rcParams['xtick.minor.size'] = 4
rcParams['ytick.minor.size'] = 4

def create_vertical_plot():
    """创建垂直版本的图表"""
    
    # 数据
    metrics_data = {
        'EN': 15,
        'Qabf': 11,
        'VIF': 10,
        'SF': 10,
        'MI': 9,
        'SSIM': 7,
        'SCD': 7,
        'CC': 6,
        'AG': 5,
        'PSNR': 3,
        'SD': 3,
        'FMI_dct': 2,
        'NCIE': 2,
        'Nabf': 2,
        'CE': 2,
        'FMI_w': 2,
        'MS_SSIM': 2,
        'EI': 2,
        'VIFF': 2,
        'Qcv': 1,
        'Qcb': 1
    }
    
    # 创建DataFrame
    df = pd.DataFrame(list(metrics_data.items()), columns=['Metric', 'Frequency'])
    
    # 创建图形 - 增大图形尺寸
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 创建颜色渐变
    colors = plt.cm.plasma(np.linspace(0.2, 0.9, len(df)))
    
    # 创建垂直条形图
    bars = ax.bar(range(len(df)), df['Frequency'], 
                  color=colors, 
                  edgecolor='white', 
                  linewidth=1.0,
                  alpha=0.85)
    
    # 添加数值标签 - 增大字体
    for i, (bar, freq) in enumerate(zip(bars, df['Frequency'])):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height + 0.2,
                f'{int(freq)}',
                ha='center', va='bottom',
                fontsize=14,  # 增大数值标签字体
                fontweight='bold',
                color='black')
    
    # 设置坐标轴 - 增大字体
    ax.set_ylabel('Usage Frequency', fontsize=22, fontweight='bold', labelpad=15)
    ax.set_xlabel('Metrics', fontsize=22, fontweight='bold', labelpad=15)
    ax.set_title('', 
                 fontsize=24, fontweight='bold', pad=30)
    
    # 设置x轴标签 - 增大字体
    ax.set_xticks(range(len(df)))
    ax.set_xticklabels(df['Metric'], rotation=45, ha='right', fontsize=16, fontweight='bold')
    
    # 设置y轴刻度标签 - 增大字体
    ax.tick_params(axis='y', labelsize=16)
    
    # 设置y轴范围
    ax.set_ylim(0, max(df['Frequency']) * 1.2)
    
    # 不显示网格线
    ax.grid(False)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('metrics_frequency_vertical_large_font.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('metrics_frequency_vertical_large_font.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    plt.show()
    
    return fig, ax

def create_vertical_plot_alternative():
    """创建另一种配色方案的垂直图表"""
    
    # 数据
    metrics_data = {
        'EN': 15,
        'Qabf': 11,
        'VIF': 10,
        'SF': 10,
        'MI': 9,
        'SSIM': 7,
        'SCD': 7,
        'CC': 6,
        'AG': 5,
        'PSNR': 3,
        'SD': 3,
        'FMI_dct': 2,
        'NCIE': 2,
        'Nabf': 2,
        'CE': 2,
        'FMI_w': 2,
        'MS_SSIM': 2,
        'EI': 2,
        'VIFF': 2,
        'Qcv': 1,
        'Qcb': 1
    }
    
    # 创建DataFrame
    df = pd.DataFrame(list(metrics_data.items()), columns=['Metric', 'Frequency'])
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 使用蓝色渐变配色
    colors = plt.cm.Blues(np.linspace(0.4, 0.9, len(df)))
    
    # 创建垂直条形图
    bars = ax.bar(range(len(df)), df['Frequency'], 
                  color=colors, 
                  edgecolor='navy', 
                  linewidth=1.2,
                  alpha=0.8)
    
    # 添加数值标签
    for i, (bar, freq) in enumerate(zip(bars, df['Frequency'])):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height + 0.2,
                f'{int(freq)}',
                ha='center', va='bottom',
                fontsize=14,
                fontweight='bold',
                color='darkblue')
    
    # 设置坐标轴
    ax.set_ylabel('Usage Frequency', fontsize=22, fontweight='bold', labelpad=15)
    ax.set_xlabel('Metrics', fontsize=22, fontweight='bold', labelpad=15)
    ax.set_title('Frequency of Metrics Usage in Image Fusion Literature', 
                 fontsize=24, fontweight='bold', pad=30)
    
    # 设置x轴标签
    ax.set_xticks(range(len(df)))
    ax.set_xticklabels(df['Metric'], rotation=45, ha='right', fontsize=16, fontweight='bold')
    
    # 设置y轴刻度标签
    ax.tick_params(axis='y', labelsize=16)
    
    # 设置y轴范围
    ax.set_ylim(0, max(df['Frequency']) * 1.2)
    
    # 不显示网格线
    ax.grid(False)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('metrics_frequency_vertical_blue.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('metrics_frequency_vertical_blue.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    plt.show()
    
    return fig, ax

def main():
    """主函数"""
    print("生成指标使用频率垂直条形图（大字体版本）...")
    
    # 生成彩色渐变版本
    print("1. 生成彩色渐变版本...")
    create_vertical_plot()
    
    # 生成蓝色主题版本
    print("2. 生成蓝色主题版本...")
    create_vertical_plot_alternative()
    
    print("\n所有图表已生成完成！")
    print("输出文件:")
    print("- metrics_frequency_vertical_large_font.png/pdf (彩色渐变)")
    print("- metrics_frequency_vertical_blue.png/pdf (蓝色主题)")
    print("\n字体已全面增大，适合SCI论文使用！")

if __name__ == "__main__":
    main()
