# 定量分析程序使用说明

## 概述

本项目包含两个定量分析程序，用于对图像优化算法进行性能分析：

1. **opt_quantitative_analysis.py** - 完整版定量分析程序
2. **test_quantitative_simple.py** - 简化版测试程序

## 功能特点

### 多尺寸多迭代测试
- **图像尺寸**: 128x128, 256x256, 512x512
- **迭代次数**: 100, 200, 500, 1000
- **测试组合**: 每个尺寸分别测试所有迭代次数 (共12个组合)

### 性能指标记录
- 总运行时间
- 平均每次迭代时间
- 损失函数收敛情况
- 图像质量指标 (SSIM, MI, Qabf, VIF, CC等)

### 可视化分析
- 运行时间对比图表
- 指标改善对比图
- 损失收敛曲线
- 时间复杂度分析

## 使用方法

### 1. 环境准备

确保安装了以下依赖：
```bash
pip install torch torchvision matplotlib pandas numpy tqdm pillow openpyxl
```

### 2. 文件结构

确保以下文件和文件夹存在：
```
项目根目录/
├── opt_quantitative_analysis.py
├── test_quantitative_simple.py
├── Losses/                    # 损失函数模块
├── input4table/
│   ├── VIS4val/              # 可见光图像
│   ├── IR4val/               # 红外图像
│   └── Fused/
│       └── ADF_val/          # 融合图像
└── quantitative_analysis/    # 输出文件夹（自动创建）
```

### 3. 运行程序

#### 方法一：运行简化测试版本（推荐先运行）
```bash
python test_quantitative_simple.py
```

这个版本只测试2个尺寸和2个迭代次数，用于验证环境是否正确配置。

#### 方法二：运行完整版本
```bash
python opt_quantitative_analysis.py
```

这个版本会测试所有12个组合，需要较长时间运行。

### 4. 参数配置

在程序开头可以修改以下参数：

```python
# 用户选择的参数
method = 'ADF_val'        # 算法名称
scene = '00280'           # 场景编号
metric = 'lr0.003'        # 学习率标识

# 超参数
lr = 0.003                # 学习率
image_sizes = [128, 256, 512]           # 测试尺寸
iteration_counts = [100, 200, 500, 1000] # 迭代次数
```

## 输出结果

### 文件输出

程序会在 `quantitative_analysis/` 文件夹下创建以下结构：

```
quantitative_analysis/
└── 00280/
    └── lr0.003/
        └── ADF_val/
            ├── comprehensive_analysis.xlsx          # 综合分析数据
            ├── quantitative_analysis_overview.png   # 综合分析图表
            ├── metrics_improvement_comparison.png   # 指标改善对比图
            ├── loss_convergence_comparison.png      # 损失收敛对比图
            └── size_128x128_iter_100/              # 各个测试组合的详细结果
                ├── 00280_ADF_val_128x128_100iter_final.png
                ├── 00280_ADF_val_128x128_100iter_history.xlsx
                └── 00280_ADF_val_128x128_100iter_milestones.xlsx
```

### 控制台输出

程序运行时会显示：
- 每个测试组合的进度条
- 实时的损失值和SSIM指标
- 完成后的汇总报告
- 时间复杂度分析

### 分析报告

程序会生成详细的分析报告，包括：

1. **性能对比表格**
   - 各尺寸和迭代次数组合的运行时间
   - 平均每次迭代时间
   - 最终损失值和指标改善

2. **时间复杂度分析**
   - 迭代次数与时间的线性关系分析
   - 不同尺寸间的复杂度估计
   - 效率评估

3. **可视化图表**
   - 运行时间对比柱状图
   - 指标改善对比图
   - 损失收敛曲线（按尺寸分组）
   - 时间复杂度散点图

## 注意事项

### 运行时间
- 完整版程序需要运行较长时间（可能数小时）
- 建议先运行简化版本验证环境
- 可以根据需要调整测试参数

### 内存要求
- 512x512图像需要较多GPU内存
- 如果内存不足，可以减少测试尺寸

### 错误排查

1. **导入错误**: 确保Losses模块在当前目录
2. **文件不存在**: 检查输入图像路径是否正确
3. **CUDA错误**: 确保GPU驱动和PyTorch版本兼容

## 自定义扩展

### 添加新的测试尺寸
```python
image_sizes = [64, 128, 256, 512, 1024]  # 添加64和1024
```

### 修改迭代次数
```python
iteration_counts = [50, 100, 200, 300, 500]  # 自定义迭代次数
```

### 添加新的指标
在 `calculate_metrics_absolute` 函数中添加新的指标计算。

## 结果解读

### 时间复杂度
- **线性复杂度** (比值 < 1.2): 算法效率高，适合大规模应用
- **平方复杂度** (比值 > 2.0): 随尺寸增长快速，需要优化

### 指标改善
- **正值**: 表示优化后指标提升
- **负值**: 表示优化后指标下降
- **数值大小**: 反映改善程度

### 收敛性能
- **快速收敛**: 损失在前期快速下降
- **稳定收敛**: 损失平稳下降到稳定值
- **震荡收敛**: 可能需要调整学习率

## 技术支持

如有问题，请检查：
1. Python环境和依赖包版本
2. 输入文件路径和格式
3. GPU内存是否充足
4. Losses模块是否正确导入
