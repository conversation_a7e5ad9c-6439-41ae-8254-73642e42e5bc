import cv2
import torchvision.transforms.functional as TF
from PIL import Image
import numpy as np
from Losses import ce_metric, en_metric, mi_metric, ag_metric, ei_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, rmse_metric, ssim_metric

method = 'ADF'
scene = 'jeep'
metric = 'mono'
i = 100
vis_path = f'input/VIS/{scene}.bmp'
ir_path = f'input/IR/{scene}.bmp'
fused_path = f'input/Fused/{scene}_{method}.png'
fused_opt_path = f'../vgg-tensorflow-master/images/{scene}/{metric}/{method}/{method}_{scene}_{i}.png'



# 读取图像并转化为灰度图像
def read_image(path):
    image = TF.to_tensor(Image.open(path)).unsqueeze(0)
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    return image

# 计算指标值
def calculate_metrics(vis, ir, fused):
    metrics = {}
    for metric_name, metric_func in metrics_functions.items():
        metrics[metric_name] = metric_func(vis, ir, fused)
    return metrics

# 计算指标涨幅
def calculate_improvement(metric_before, metric_after):
    if metric_before == 0:
        return 0
    return ((metric_after - metric_before) / metric_before) * 100

# 定义指标名称和计算函数的字典
metrics_functions = {
    'Cross Entropy': ce_metric,
    'Entropy': en_metric,
    'Mutual Info': mi_metric, 
    'PSNR': psnr_metric,
    'Avg Gradient': ag_metric,
    'Edge Intensity': ei_metric,
    'Qabf': q_abf_metric,
    'SD': sd_metric,
    'Spatial Freq': sf_metric,
    'Rmse': rmse_metric,
    'SSIM': ssim_metric,
}

# 读取图像
vis = read_image(vis_path)
ir = read_image(ir_path)
fused = read_image(fused_path)
fused_opt = read_image(fused_opt_path)

# 计算原始融合图指标
metrics_fused = calculate_metrics(vis, ir, fused)

# 计算优化后融合图指标
metrics_fused_opt = calculate_metrics(vis, ir, fused_opt)

# 计算指标涨幅
improvements = {}
for metric_name in metrics_functions.keys():
    improvements[metric_name] = calculate_improvement(metrics_fused[metric_name], metrics_fused_opt[metric_name])

# 打印指标
print("原始融合图指标:")
for metric_name, metric_value in metrics_fused.items():
    print(f"{metric_name}: {metric_value}")

print("\n优化后融合图指标:")
for metric_name, metric_value in metrics_fused_opt.items():
    print(f"{metric_name}: {metric_value}")

print("\n指标改善百分比:")
for metric_name, improvement in improvements.items():
    print(f"{metric_name}: {improvement:.2f} %")

# 保存到Excel表格中
# import pandas as pd

# data = {
#     'Metric': list(metrics_functions.keys()),
#     'Original Fused Image': list(metrics_fused.values()),
#     'Optimized Fused Image': list(metrics_fused_opt.values()),
#     'Improvement (%)': list(improvements.values())
# }

# df = pd.DataFrame(data)
# df.to_excel('metrics.xlsx', index=False)