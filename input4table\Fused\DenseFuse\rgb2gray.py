import os
from PIL import Image

# 设置图像文件夹路径
image_folder = "F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\Fused\DenseFuse"

# 获取指定文件夹中的所有文件名
file_names = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]

# 遍历每个文件
for file_name in file_names:
    # 检查文件是否为图像文件（支持的格式可以根据需要进行扩展）
    if file_name.endswith(('.png', '.jpg', '.jpeg')):
        # 打开图像文件
        with Image.open(os.path.join(image_folder, file_name)) as img:
            # 转换为灰度图像
            gray_img = img.convert('L')
            # 设置保存的文件名
            save_name = os.path.join(image_folder, f"{os.path.splitext(file_name)[0]}{os.path.splitext(file_name)[1]}")
            # 保存灰度图像
            gray_img.save(save_name)
            print(f"Converted {file_name} to {save_name}")
    else:
        print(f"Skipping {file_name}: Not a supported image format")

print("Conversion completed.")
