import os

def rename_files_in_dir(directory):
    for filename in os.listdir(directory):
        # 检查是否是文件
        if os.path.isfile(os.path.join(directory, filename)):
            # 提取n值
            name, extension = os.path.splitext(filename)
            n = name.split('_')[0]  # 假设原文件名格式为n_xxx.png，n值为下划线前的部分
            # 构造新文件名
            new_filename = f"{n}_SwinFuse.png"  # 使用n值构造新文件名
            # 重命名文件
            os.rename(os.path.join(directory, filename), os.path.join(directory, new_filename))
            print(f"Renamed '{filename}' to '{new_filename}'")

# 使用当前目录或指定目录
current_directory = os.getcwd()
current_directory = "F:\\.Infrared and Visiable Light Fusion\\Optimization based\\input4table\\Fused\\SwinFuse"  # 如果需要，替换为特定路径

rename_files_in_dir(current_directory)
