import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
import numpy as np
from Losses import ei

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

method = 'CBF'
scene = 'jeep'
metric = 'EI'
vis_path = 'input/VIS/jeep.bmp'
ir_path = 'input/IR/jeep.bmp'
fused_path = f'input/Fused/{scene}_{method}.png'


transform = transforms.Compose(
    [
        transforms.ToTensor(),
    ]
)

normalize = transforms.Normalize(mean=[0.5], std=[0.5])

vis = TF.to_tensor(Image.open(vis_path)).unsqueeze(0).to(device)
ir = TF.to_tensor(Image.open(ir_path)).unsqueeze(0).to(device)
fused = TF.to_tensor(Image.open(fused_path)).unsqueeze(0).to(device)

if vis.shape[1] == 3:
    vis = vis.mean(dim=1, keepdim=True)
if ir.shape[1] == 3:
    ir = ir.mean(dim=1, keepdim=True)
if fused.shape[1] == 3:
    fused = fused.mean(dim=1, keepdim=True)

output_folder = f"../vgg-tensorflow-master/images/{scene}/{metric}/{method}/"

# 将要优化的参数初始化为融合图像
optimized_fused = nn.Parameter(fused.clone())

# 定义SSIM损失函数
# def edge_int(x):
#     return ei.edge_intensity(x)

# def edge_int_loss(x):
#     return ei.edge_intensity_loss(x)

def edge_intensity(x):
    return ei.edge_intensity(x)

def edge_intensity_loss(x):
    return 1-(ei.edge_intensity(x) - 28 ) / 50



# edge_int = edge_intensity(fused)
# # edge_int2 = edge_intensity(optimized_fused)
# print(edge_int)


# 定义优化器
optimizer = optim.Adam([optimized_fused], lr=0.00005)

# 训练迭代次数
num_iterations = 10100

# 设置图像窗口的大小
fig = plt.figure(figsize=(6, 4), dpi=300)

# 两个子图的初始状态
original_subplt = plt.subplot(121)
optimized_subplt = plt.subplot(122)

# 添加视频录制功能
video_path = f'{method}_{scene}_{metric}.mp4'
writer = imageio.get_writer(video_path, fps=10)

losses = []
ei_val = []

for i in range(num_iterations):

    current_ei_val = edge_intensity(optimized_fused)
    current_ei_loss = edge_intensity_loss(optimized_fused)
    
    # 反向传播优化
    optimizer.zero_grad()
    current_ei_loss.backward()
    optimizer.step()

    # 保存损失+ssim值
    losses.append(current_ei_loss.item())
    ei_val.append(current_ei_val.item())
    
    # 打印优化进度
    if i % 100 == 0: 
        print(f"Iteration {i}, EI_loss: {current_ei_loss.item()}, EI: {current_ei_val.item()}")

        # 保存优化图像
        optimized_image = optimized_fused.detach().clone().clamp(0, 1)
        optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
        optimized_image_display = optimized_image_display.astype(np.uint8)  # 将图像数据转换为uint8类型
        optimized_image_path = f"{output_folder}{method}_{scene}_{i}.png"
        TF.to_pil_image(optimized_image.squeeze(0).cpu()).save(optimized_image_path)

        # 显示图像
        original_subplt.clear()
        optimized_subplt.clear()

        original_subplt.imshow(fused.squeeze(0).cpu().numpy()[0], cmap='gray')
        optimized_subplt.imshow(optimized_image_display, cmap='gray')

        # 设置标题和关闭坐标轴
        original_subplt.set_title("Original Fused Image")
        optimized_subplt.set_title(f"Iteration: {i}")
        original_subplt.axis('off')
        optimized_subplt.axis('off')
        
        plt.pause(0.1)
        
        # 添加图像到视频
        writer.append_data(plt.imread(optimized_image_path))



# 关闭图像窗口
plt.close(fig)

# 关闭视频写入器
writer.close()
print(f"Video saved to {video_path}")

# 绘制损失曲线
plt.figure(figsize=(8, 5))
plt.plot(losses, label='EI Loss')
plt.title('EI Loss ')
plt.xlabel('Iteration')
plt.ylabel('EI Loss')
plt.legend()

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(ei_val, label='EI Value')
plt.title('EI Value')
plt.xlabel('Iteration')
plt.ylabel('EI Value')
plt.legend()
plt.show()
