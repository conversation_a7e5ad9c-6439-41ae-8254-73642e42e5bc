import cv2
import torchvision.transforms.functional as TF
from PIL import Image
import numpy as np
import torch
from Losses import ce_metric
from Losses import en_metric
from Losses import mi_metric
from Losses import ag_metric
from Losses import ei_metric
from Losses import psnr_metric
from Losses import q_abf_metric
from Losses import sd_metric
from Losses import rmse_metric
from Losses import ssim_metric
from Losses import sf_metric
from Losses import q_cb_metric
from Losses import scd_metric
from Losses import vif_metric
from Losses import cc_metric


method = 'Baysian'
scene = '2'
# metric = 'testhyp'
# i = 200
# if scene=='jeep' or scene=="kaptein":
#     vis_path = f'input/VIS/{scene}.bmp'
#     ir_path = f'input/IR/{scene}.bmp'
# else:
#     vis_path = f'input/VIS/{scene}.png'
#     ir_path = f'input/IR/{scene}.png'

vis_path = f"F:\\.Infrared and Visiable Light Fusion\Optimization based\\input4table\\VIS\\{scene}.png"
ir_path = f"F:\\.Infrared and Visiable Light Fusion\Optimization based\\input4table\\IR\\{scene}.png"

# fused_path = f'input/Fused/{scene}/{scene}_{method}.png'
# fused_opt_path = f'../vgg-tensorflow-master/images/{scene}/{metric}/{method}/{scene}_{i}.png'
fused_path = f'F:\\.Infrared and Visiable Light Fusion\\Optimization based\\input4table\\Fused\\{method}\\{scene}_{method}.png'
fused_opt_path = f'F:\\.Infrared and Visiable Light Fusion\\Optimization based\\output\\{method}\\{scene}_{method}_opt.png'
# fused_opt_path = f'../vgg-tensorflow-master/images/{scene}/{metric}/{method}/matched_image.png'


# 读取图像并转化为灰度图像
def read_image(path):
    # 使用PIL读取图像
    img = Image.open(path)
    # 转换为灰度图像（如果不是灰度图像）
    if img.mode != 'L':
        img = img.convert('L')
    # 转换为tensor并添加batch维度
    img_tensor = TF.to_tensor(img).unsqueeze(0)
    # 移动到GPU
    return img_tensor.cuda()


# 调整图像尺寸使其一致，以fused_opt的尺寸为基准
def resize_images(vis, ir, fused, fused_opt):
    # 获取优化后图像的尺寸作为基准
    reference_h, reference_w = fused_opt.shape[-2], fused_opt.shape[-1]
    
    # 调整可见光图像的尺寸
    if vis.shape[-2] != reference_h or vis.shape[-1] != reference_w:
        vis = torch.nn.functional.interpolate(
            vis, 
            size=(reference_h, reference_w), 
            mode='bilinear', 
            align_corners=False
        )
    
    # 调整红外图像的尺寸
    if ir.shape[-2] != reference_h or ir.shape[-1] != reference_w:
        ir = torch.nn.functional.interpolate(
            ir, 
            size=(reference_h, reference_w), 
            mode='bilinear', 
            align_corners=False
        )
    
    # 调整原始融合图像的尺寸
    if fused.shape[-2] != reference_h or fused.shape[-1] != reference_w:
        fused = torch.nn.functional.interpolate(
            fused, 
            size=(reference_h, reference_w), 
            mode='bilinear', 
            align_corners=False
        )
    
    return vis, ir, fused, fused_opt


def calculate_metrics(vis, ir, fused):
    metrics = {}
    for metric_name, metric_func in metrics_functions.items():
        metrics[metric_name] = metric_func(vis, ir, fused).cuda()  # 将计算结果移到 GPU 上
    return metrics

# 计算指标涨幅
def calculate_improvement(metric_before, metric_after, metric_name):
    if metric_name in ['CE']:
        # 对于越小越好的指标，涨幅百分比应该是负的，如果降低了则是正的
        return -((metric_after - metric_before) / metric_before) * 100
    else:
        if metric_before == 0:
            return 0
        # 对于越大越好的指标，涨幅百分比应该是正的，如果提高了则是正的
        return ((metric_after - metric_before) / metric_before) * 100

# 定义指标名称和计算函数的字典
metrics_functions = {
    'CE': ce_metric,
    'EN': en_metric,
    'MI': mi_metric, 
    'PSNR': psnr_metric,
    # 'AG': ag_metric,
    'EI': ei_metric,
    'Qabf': q_abf_metric,
    'SD': sd_metric,
    'SF': sf_metric,
    'RMSE': rmse_metric,
    'SSIM': ssim_metric,
    'Qcb': q_cb_metric,
    'VIF': vif_metric,
    'SCD': scd_metric,
    'CC': cc_metric
}

# 读取图像
vis = read_image(vis_path)
ir = read_image(ir_path)
fused = read_image(fused_path)
fused_opt = read_image(fused_opt_path)

# 打印调整前的图像尺寸
print("\n调整前图像尺寸:")
print(f"可见光图像: {vis.shape[-2]}x{vis.shape[-1]}")
print(f"红外图像: {ir.shape[-2]}x{ir.shape[-1]}")
print(f"原始融合图像: {fused.shape[-2]}x{fused.shape[-1]}")
print(f"优化后融合图像: {fused_opt.shape[-2]}x{fused_opt.shape[-1]}")

# 调整图像尺寸使其一致
vis, ir, fused, fused_opt = resize_images(vis, ir, fused, fused_opt)

# 打印调整后的图像尺寸
print("\n调整后图像尺寸:")
print(f"可见光图像: {vis.shape[-2]}x{vis.shape[-1]}")
print(f"红外图像: {ir.shape[-2]}x{ir.shape[-1]}")
print(f"原始融合图像: {fused.shape[-2]}x{fused.shape[-1]}")
print(f"优化后融合图像: {fused_opt.shape[-2]}x{fused_opt.shape[-1]}")
print("所有图像已调整为与优化后融合图像相同的尺寸\n")

# 计算原始融合图指标
metrics_fused = calculate_metrics(vis, ir, fused)

# 计算优化后融合图指标
metrics_fused_opt = calculate_metrics(vis, ir, fused_opt)

# 计算指标涨幅
improvements = {}
for metric_name in metrics_functions.keys():
    improvements[metric_name] = calculate_improvement(metrics_fused[metric_name], metrics_fused_opt[metric_name], metric_name)

# 打印指标
print("原始融合图指标:")
for metric_name, metric_value in metrics_fused.items():
    print(f"{metric_name}: {metric_value}")

print("\n优化后融合图指标:")
for metric_name, metric_value in metrics_fused_opt.items():
    print(f"{metric_name}: {metric_value}")

print("\n指标涨幅:")
for metric_name, improvement in improvements.items():
    print(f"{metric_name}: {improvement:.2f} %")

import pandas as pd

# 将所有的tensor转移到 CPU 上并转换为 NumPy 数组
vis = vis.cpu().detach().numpy()
ir = ir.cpu().detach().numpy()
fused = fused.cpu().detach().numpy()
fused_opt = fused_opt.cpu().detach().numpy()


change_percentages_formatted = [f"{value:.2f}%" for value in improvements.values()]

output_data = {
    'Metric': list(metrics_functions.keys()),
    'Ori': [float(value) for value in metrics_fused.values()],
    'Opt': [float(value) for value in metrics_fused_opt.values()],
    'Per': change_percentages_formatted
}

output_df = pd.DataFrame(output_data)
output_df.to_excel(f'output_metrics.xlsx', index=False)
print('\nexcel已保存\n')