import cv2
import torchvision.transforms.functional as TF
from PIL import Image
import numpy as np
import torch
import os
import pandas as pd
import glob
from tqdm import tqdm
from Losses import ce_metric
from Losses import en_metric
from Losses import mi_metric
from Losses import ag_metric
from Losses import ei_metric
from Losses import psnr_metric
from Losses import q_abf_metric
from Losses import sd_metric
from Losses import rmse_metric
from Losses import ssim_metric
from Losses import sf_metric
from Losses import q_cb_metric
from Losses import scd_metric
from Losses import vif_metric
from Losses import cc_metric

# 配置参数 - 算法列表
methods = ['ADF', 'Baysian', 'CDDFuse', 'DDcGAN', 'DenseFuse', 'DIDFuse', 'DIVF', 'FusionGAN', 'GTF', 'HMSD_GF', 'Res2Fusion', 'RFN-Nest', 'SwinFuse', 'TarDAL', 'U2Fusion']  # 可以根据需要添加或删除算法

# 基础路径
base_path = "F:\\.Infrared and Visiable Light Fusion\\Optimization based"
vis_base_path = f"{base_path}\\input4table\\VIS"
ir_base_path = f"{base_path}\\input4table\\IR"

# 定义指标名称和计算函数的字典
metrics_functions = {
    'CE': ce_metric,
    'EN': en_metric,
    'MI': mi_metric, 
    'PSNR': psnr_metric,
    # 'AG': ag_metric,
    'EI': ei_metric,
    'Qabf': q_abf_metric,
    'SD': sd_metric,
    'SF': sf_metric,
    'RMSE': rmse_metric,
    'SSIM': ssim_metric,
    'Qcb': q_cb_metric,
    'VIF': vif_metric,
    'SCD': scd_metric,
    'CC': cc_metric
}

# 读取图像并转化为灰度图像
def read_image(path):
    # 使用PIL读取图像
    img = Image.open(path)
    # 转换为灰度图像（如果不是灰度图像）
    if img.mode != 'L':
        img = img.convert('L')
    # 转换为tensor并添加batch维度
    img_tensor = TF.to_tensor(img).unsqueeze(0)
    # 移动到GPU
    return img_tensor.cuda()

# 调整图像尺寸使其一致，以fused_opt的尺寸为基准
def resize_images(vis, ir, fused, fused_opt):
    # 获取优化后图像的尺寸作为基准
    reference_h, reference_w = fused_opt.shape[-2], fused_opt.shape[-1]
    
    # 调整可见光图像的尺寸
    if vis.shape[-2] != reference_h or vis.shape[-1] != reference_w:
        vis = torch.nn.functional.interpolate(
            vis, 
            size=(reference_h, reference_w), 
            mode='bilinear', 
            align_corners=False
        )
    
    # 调整红外图像的尺寸
    if ir.shape[-2] != reference_h or ir.shape[-1] != reference_w:
        ir = torch.nn.functional.interpolate(
            ir, 
            size=(reference_h, reference_w), 
            mode='bilinear', 
            align_corners=False
        )
    
    # 调整原始融合图像的尺寸
    if fused.shape[-2] != reference_h or fused.shape[-1] != reference_w:
        fused = torch.nn.functional.interpolate(
            fused, 
            size=(reference_h, reference_w), 
            mode='bilinear', 
            align_corners=False
        )
    
    return vis, ir, fused, fused_opt

# 计算指标
def calculate_metrics(vis, ir, fused):
    metrics = {}
    for metric_name, metric_func in metrics_functions.items():
        value = metric_func(vis, ir, fused).cuda()
        # 将tensor转换为标量
        if isinstance(value, torch.Tensor):
            metrics[metric_name] = value.item()
        else:
            metrics[metric_name] = float(value)
    return metrics

# 计算指标涨幅
def calculate_improvement(metric_before, metric_after, metric_name):
    if metric_name in ['CE', 'RMSE']:  # 越小越好的指标
        # 对于越小越好的指标，涨幅百分比应该是负的，如果降低了则是正的
        improvement = -((metric_after - metric_before) / metric_before) * 100
    else:  # 越大越好的指标
        if metric_before == 0:
            improvement = torch.tensor(0.0).cuda() if isinstance(metric_before, torch.Tensor) else 0.0
        else:
            # 对于越大越好的指标，涨幅百分比应该是正的，如果提高了则是正的
            improvement = ((metric_after - metric_before) / metric_before) * 100
    
    # 将结果转换为标量
    if isinstance(improvement, torch.Tensor):
        return improvement.item()
    return float(improvement)

# 获取所有需要处理的图像文件
def get_all_image_pairs(method):
    # 设置当前方法的路径
    fused_base_path = f"{base_path}\\input4table\\Fused\\{method}"
    fused_opt_base_path = f"{base_path}\\output\\{method}"
    
    # 获取所有原始融合图像文件路径
    fused_files = glob.glob(f"{fused_base_path}\\*_{method}.png")
    
    # 提取场景标识符
    image_pairs = []
    for fused_file in fused_files:
        # 从路径中提取文件名
        file_name = os.path.basename(fused_file)
        # 提取场景标识符 (例如: "1_Baysian.png" -> "1")
        scene_id = file_name.split('_')[0]
        
        # 构建对应的其他图像路径
        vis_file = f"{vis_base_path}\\{scene_id}.png"
        ir_file = f"{ir_base_path}\\{scene_id}.png"
        fused_opt_file = f"{fused_opt_base_path}\\{scene_id}_{method}_opt.png"
        
        # 检查所有文件是否存在
        if os.path.exists(vis_file) and os.path.exists(ir_file) and os.path.exists(fused_opt_file):
            image_pairs.append({
                'scene_id': scene_id,
                'vis_path': vis_file,
                'ir_path': ir_file,
                'fused_path': fused_file,
                'fused_opt_path': fused_opt_file
            })
    
    return image_pairs

# 处理单个图像对
def process_image_pair(image_pair):
    scene_id = image_pair['scene_id']
    
    # 读取图像
    vis = read_image(image_pair['vis_path'])
    ir = read_image(image_pair['ir_path'])
    fused = read_image(image_pair['fused_path'])
    fused_opt = read_image(image_pair['fused_opt_path'])
    
    # 调整图像尺寸使其一致
    vis, ir, fused, fused_opt = resize_images(vis, ir, fused, fused_opt)
    
    # 计算原始融合图指标
    metrics_fused = calculate_metrics(vis, ir, fused)
    
    # 计算优化后融合图指标
    metrics_fused_opt = calculate_metrics(vis, ir, fused_opt)
    
    # 计算指标涨幅
    improvements = {}
    for metric_name in metrics_functions.keys():
        improvements[metric_name] = calculate_improvement(
            metrics_fused[metric_name], 
            metrics_fused_opt[metric_name], 
            metric_name
        )
    
    # 准备返回结果
    result = {
        'scene_id': scene_id,
        'metrics_ori': {k: v for k, v in metrics_fused.items()},
        'metrics_opt': {k: v for k, v in metrics_fused_opt.items()},
        'improvements': improvements
    }
    
    return result

# 处理单个算法
def process_method(method):
    print("\n" + "="*50)
    print(f"开始处理 {method} 算法的图像指标对比")
    print("="*50)
    
    # 获取所有需要处理的图像对
    image_pairs = get_all_image_pairs(method)
    
    if not image_pairs:
        print(f"\n❌ 没有找到 {method} 算法的有效图像数据，跳过")
        return None
    
    print(f"\n📊 找到 {len(image_pairs)} 对图像需要处理")
    print("\n⏳ 开始处理图像...")
    
    # 处理所有图像对，使用进度条显示进度
    all_results = []
    for image_pair in tqdm(image_pairs, desc=f"{method} 处理进度", ncols=100, colour="green"):
        result = process_image_pair(image_pair)
        all_results.append(result)
    
    print(f"\n✅ {method} 图像处理完成!")
    
    # 计算平均指标值和平均提升百分比
    avg_metrics_ori = {metric: 0.0 for metric in metrics_functions.keys()}
    avg_metrics_opt = {metric: 0.0 for metric in metrics_functions.keys()}
    avg_improvements = {metric: 0.0 for metric in metrics_functions.keys()}
    
    for result in all_results:
        for metric in metrics_functions.keys():
            avg_metrics_ori[metric] += result['metrics_ori'][metric]
            avg_metrics_opt[metric] += result['metrics_opt'][metric]
            avg_improvements[metric] += result['improvements'][metric]
    
    image_count = len(all_results)
    for metric in metrics_functions.keys():
        avg_metrics_ori[metric] /= image_count
        avg_metrics_opt[metric] /= image_count
        avg_improvements[metric] /= image_count
    
    # 创建Excel表格数据 - 只包含平均值
    excel_data = []
    
    # 为每个指标创建一行数据
    for metric in metrics_functions.keys():
        # 确保所有值都是标量
        ori_value = avg_metrics_ori[metric]
        opt_value = avg_metrics_opt[metric]
        imp_value = avg_improvements[metric]
        
        metric_data = {
            '指标名称': metric,
            '优化前平均值': ori_value,
            '优化后平均值': opt_value,
            '平均提升(%)': imp_value
        }
        excel_data.append(metric_data)
    
    # 创建DataFrame并保存
    df = pd.DataFrame(excel_data)
    output_excel = f"{method}_metrics_comparison.xlsx"
    df.to_excel(output_excel, index=False)
    
    print(f"\n {method} 算法的 {image_count} 张图像指标对比已保存到 {output_excel}")
    
    # 打印平均指标变化
    print("\n 指标平均值对比:")
    print("-"*60)
    print(f"{'指标名称':<10} | {'优化前平均值':<15} | {'优化后平均值':<15} | {'平均提升(%)':<12}")
    print("-"*60)
    for metric in metrics_functions.keys():
        print(f"{metric:<10} | {avg_metrics_ori[metric]:<15.4f} | {avg_metrics_opt[metric]:<15.4f} | {avg_improvements[metric]:<+12.2f}")
    print("-"*60)
    
    return {
        'method': method,
        'image_count': image_count,
        'avg_metrics_ori': avg_metrics_ori,
        'avg_metrics_opt': avg_metrics_opt,
        'avg_improvements': avg_improvements
    }

# 创建比较不同算法的汇总Excel表格
def create_methods_comparison_excel(method_results):
    # 创建比较数据
    comparison_data = []
    
    # 添加每个指标的比较数据
    for metric in metrics_functions.keys():
        metric_row = {'指标名称': metric}
        
        # 添加每个算法的数据
        for result in method_results:
            method_name = result['method']
            metric_row[f"{method_name}_优化前"] = result['avg_metrics_ori'][metric]
            metric_row[f"{method_name}_优化后"] = result['avg_metrics_opt'][metric]
            metric_row[f"{method_name}_提升(%)"] = result['avg_improvements'][metric]
        
        comparison_data.append(metric_row)
    
    # 创建DataFrame并保存
    df = pd.DataFrame(comparison_data)
    output_excel = "all_methods_comparison.xlsx"
    df.to_excel(output_excel, index=False)
    
    print(f"\n📈 所有算法的综合对比已保存到 {output_excel}")

# 主函数
def main():
    print("\n" + "="*50)
    print(f"开始处理多个算法的图像指标对比")
    print("="*50)
    
    # 存储所有算法的结果
    all_method_results = []
    
    # 依次处理每个算法
    for method in methods:
        method_result = process_method(method)
        if method_result:
            all_method_results.append(method_result)
    
    # 如果成功处理了至少一个算法，则创建综合对比表
    if all_method_results:
        create_methods_comparison_excel(all_method_results)
        
        print("\n" + "="*50)
        print(f"所有算法指标分析完成!")
        print("="*50)
    else:
        print("\n❌ 没有找到任何有效的算法数据")

if __name__ == "__main__":
    main() 