import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
import numpy as np
from Losses import ssim
from Losses import mi
from Losses import ei
from Losses import psnr
from Losses import en
from Losses import ce1
from Losses import ag
from Losses import rmse
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import q_cb


os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

method = 'GFCE'
scene = 'jeep'
metric = 'SF'
vis_path = f'input/VIS/{scene}.bmp'
ir_path = f'input/IR/{scene}.bmp'
fused_path = f'input/Fused/{scene}_{method}.png'

# set hyperparameter
lr = 0.001

num_iterations = 5000


transform = transforms.Compose(
    [
        transforms.ToTensor(),
    ]
)

normalize = transforms.Normalize(mean=[0.5], std=[0.5])

vis = TF.to_tensor(Image.open(vis_path)).unsqueeze(0).to(device)
ir = TF.to_tensor(Image.open(ir_path)).unsqueeze(0).to(device)
fused = TF.to_tensor(Image.open(fused_path)).unsqueeze(0).to(device)

if vis.shape[1] == 3:
    vis = vis.mean(dim=1, keepdim=True)
if ir.shape[1] == 3:
    ir = ir.mean(dim=1, keepdim=True)
if fused.shape[1] == 3:
    fused = fused.mean(dim=1, keepdim=True)

output_folder = f"../vgg-tensorflow-master/images/{scene}/{metric}/{method}/"

# 将要优化的参数初始化为融合图像
optimized_fused = nn.Parameter(fused.clone())

def sf_loss(x,y,z):
    vis = sf.sf_approach_loss(x,z)
    ir = sf.sf_approach_loss(y,z)
    sf_total_loss = torch.log10((vis+ir) / 2 + 1e-9)
    return -sf_total_loss
    

# 定义优化器
optimizer = optim.Adam([optimized_fused], lr)

# 训练迭代次数


# 设置图像窗口的大小
fig = plt.figure(figsize=(6, 4), dpi=300)

# 两个子图的初始状态
original_subplt = plt.subplot(121)
optimized_subplt = plt.subplot(122)

# 添加视频录制功能
video_path = 'output_video.mp4'
writer = imageio.get_writer(video_path, fps=10)

sf_losses = []

for i in range(num_iterations):

    # loss of metrics
    current_sf_loss = sf_loss(vis, ir, optimized_fused)

    
    # 反向传播优化
    optimizer.zero_grad()
    current_sf_loss.backward()
    optimizer.step()

    # 保存 losses
    sf_losses.append(current_sf_loss.item())

    
    # 保存 metrics value
    
    # ce_value.append(current_ce_val.item())
    # en_value.append(current_en_val.item())
    # mi_value.append(current_mi_val.item())
    # ag_value.append(current_ag_val.item())
    # ei_value.append(current_ei_val.item())
    # qabf_value.append(current_qabf_val.item())
    # sd_value.append(current_sd_val.item())
    # sf_value.append(current_sf_val.item())
    # rmse_value.append(current_rmse_val.item())
    # ssim_value.append(current_ssim_val.item())
    # qcb_value.append(current_qcb_val.item())

    
    # 打印优化进度
    if i % 100 == 0: 
        print(f"Iteration {i + 100}")
        print(f"SSIM_loss: {current_sf_loss.item()}")

        # print(f"CE Value:{current_ce_val.item()}")
        # print(f"EN Value:{current_en_val.item()}")
        # print(f"MI Value:{current_mi_val.item()}")
        # print(f"PSNR Value:{current_psnr_val.item()}")
        # print(f"AG Value:{current_ag_val.item()}")
        # print(f"EI Value:{current_ei_val.item()}")
        # print(f"Qabf Value:{current_qabf_val.item()}")
        # print(f"SD Value:{current_sd_val.item()}")
        # print(f"SF Value:{current_sf_val.item()}")
        # print(f"RMSE Value:{current_rmse_val.item()}")
        # print(f"SSIM Value:{current_ssim_val.item()}")
        # print(f"Qcb Value:{current_qcb_val.item()}")
        print("\n")

        # 保存优化图像
        optimized_image = optimized_fused.detach().clone().clamp(0, 1)
        optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
        optimized_image_display = optimized_image_display.astype(np.uint8)  # 将图像数据转换为uint8类型
        optimized_image_path = f"{output_folder}{method}_{scene}_{i + 100}.png"
        os.makedirs(output_folder, exist_ok=True)
        TF.to_pil_image(optimized_image.squeeze(0).cpu()).save(optimized_image_path)

        # 显示图像
        original_subplt.clear()
        optimized_subplt.clear()

        original_subplt.imshow(fused.squeeze(0).cpu().numpy()[0], cmap='gray')
        optimized_subplt.imshow(optimized_image_display, cmap='gray')

        # 设置标题和关闭坐标轴
        original_subplt.set_title("Original Fused Image")
        optimized_subplt.set_title(f"Iteration: {i + 100}")
        original_subplt.axis('off')
        optimized_subplt.axis('off')
        
        plt.pause(0.1)
        
        # 添加图像到视频
        writer.append_data(plt.imread(optimized_image_path))



# 关闭图像窗口
plt.close(fig)

# 关闭视频写入器
writer.close()
print(f"Video saved to {video_path}")

# 绘制损失曲线
plt.figure(figsize=(8, 5))
plt.plot(sf_losses, label='SF Loss')
plt.title('SF Loss ')
plt.xlabel('Iteration')
plt.ylabel('SF Loss')
plt.legend()
plt.show()
plt.savefig(os.path.join(output_folder, 'sf_loss.png'))
plt.close

 
