import os

# 设置红外图像和可见光图像的文件夹路径
ir_image_dir = 'F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\IR'
vis_image_dir = 'F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\VIS'

# 获取红外和可见光图像的文件名（不包括文件扩展名）
ir_files = os.listdir(ir_image_dir)
vis_files = os.listdir(vis_image_dir)

# 按照排序进行重命名
for i, (ir_file, vis_file) in enumerate(zip(ir_files, vis_files), start=1):
    # 构建新的文件名
    ir_ext = os.path.splitext(ir_file)[1]
    new_ir_name = f"x_{i}{ir_ext}"
    vis_ext = os.path.splitext(vis_file)[1]
    new_vis_name = f"x_{i}{vis_ext}"

    # 重命名红外图像和可见光图像
    os.rename(os.path.join(ir_image_dir, ir_file), os.path.join(ir_image_dir, new_ir_name))
    os.rename(os.path.join(vis_image_dir, vis_file), os.path.join(vis_image_dir, new_vis_name))
    
    print(f'Renamed {ir_file} to {new_ir_name}')
    print(f'Renamed {vis_file} to {new_vis_name}')

print('Renaming completed!')
