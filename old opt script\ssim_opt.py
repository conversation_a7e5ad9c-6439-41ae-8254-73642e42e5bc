import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
import numpy as np
from Losses import ssim
from Losses import mi
from Losses import ei
from Losses import psnr
from Losses import en
from Losses import ce1
from Losses import ag
from Losses import rmse
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import q_cb


os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

method = 'FPDE'
scene = 'shop'
metric = 'SSIM'
vis_path = f'input/VIS/{scene}.png'
ir_path = f'input/IR/{scene}.png'
fused_path = f'input/Fused/{scene}_{method}.png'

# set hyperparameter
lr = 0.0003
# w_ssim = 2.0
# w_mi = 0.005
# w_qabf = 1.5
# w_psnr = 0.1
# w_sf = 0.01
# w_en = 0.0
num_iterations = 100


transform = transforms.Compose(
    [
        transforms.ToTensor(),
    ]
)

normalize = transforms.Normalize(mean=[0.5], std=[0.5])

vis = TF.to_tensor(Image.open(vis_path)).unsqueeze(0).to(device)
ir = TF.to_tensor(Image.open(ir_path)).unsqueeze(0).to(device)
fused = TF.to_tensor(Image.open(fused_path)).unsqueeze(0).to(device)

if vis.shape[1] == 3:
    vis = vis.mean(dim=1, keepdim=True)
if ir.shape[1] == 3:
    ir = ir.mean(dim=1, keepdim=True)
if fused.shape[1] == 3:
    fused = fused.mean(dim=1, keepdim=True)

output_folder = f"../vgg-tensorflow-master/images/{scene}/{metric}/{method}/"

# 将要优化的参数初始化为融合图像
optimized_fused = nn.Parameter(fused.clone())

# 定义SSIM损失函数
# def ssim_loss(x, y, z):
#     vis = ssim.ssim_approach_loss(x,z)
#     ir = ssim.ssim_approach_loss(y,z)
#     ssim_total_loss = (vis + ir) / 2
#     return ssim_total_loss

def ssim_loss(x, y, z):
    ssim_loss_vis = kornia.losses.ssim_loss(x, z, window_size=11)
    ssim_loss_ir = kornia.losses.ssim_loss(y, z, window_size=11)
    ssim_loss_total = (ssim_loss_vis + ssim_loss_ir) / 2
    return ssim_loss_total

def mi_loss(x,y,z):
    vis = mi.mi(x,z)
    ir = mi.mi(y,z)
    mi_total_loss = ( vis + ir ) / 2
    return -mi_total_loss

def qabf_loss(x,y,z):
    return -qabf.q_abf(x,y,z)

def psnr_loss(x,y,z):
    return -psnr.psnr(x,y,z)

def sf_loss(x,y,z):
    vis = sf.sf_approach_loss(x,z)
    ir = sf.sf_approach_loss(y,z)
    sf_total_loss = torch.log10((vis+ir) / 2 + 1e-9)
    return -sf_total_loss

def en_loss(x,y,z):
    vis = en.en_approach_loss(x,z)
    ir = en.en_approach_loss(y,z)
    en_total_loss = (vis+ir) / 2
    return -en_total_loss
    

# 定义指标的真实值
def ssim_val(x, y, z):
    return ssim.ssim_metric(x, y, z)
def ce_val(x, y, z):
    return ce1.ce_metric(x, y, z)
def en_val(x, y, z):
    return en.en_metric(x, y, z)
def mi_val(x, y, z):
    return mi.mi_metric(x, y, z)
def psnr_val(x, y, z):
    return psnr.psnr_metric(x, y, z)
def ei_val(x, y, z):
    return ei.ei_metric(x, y, z)
def ag_val(x, y, z):
    return ag.ag_metric(x, y, z)
def qabf_val(x, y, z):
    return qabf.q_abf_metric(x, y, z)
def sf_val(x, y, z):
    return sf.sf_metric(x, y, z)
def sd_val(x, y, z):
    return sd.sd_metric(x, y, z)
def rmse_val(x, y, z):
    return rmse.rmse_metric(x, y, z)
def qcb_val(x, y, z):
    return q_cb.q_cb_metric(x, y, z)

# 定义优化器
optimizer = optim.Adam([optimized_fused], lr)



# 设置图像窗口的大小
fig = plt.figure(figsize=(6, 4), dpi=250)

# 两个子图的初始状态
original_subplt = plt.subplot(121)
optimized_subplt = plt.subplot(122)

# 添加视频录制功能
video_path = f'{metric}_{scene}_{method}_lr{lr}.mp4'
writer = imageio.get_writer(video_path, fps=10)

ssim_losses = []
ssim_value, ce_value, en_value, mi_value, psnr_value, ag_value, ei_value, qabf_value, sd_value, sf_value, rmse_value, qcb_value= [],[],[],[],[],[],[],[],[],[],[],[]

for i in range(num_iterations):

    # loss of metrics
    current_ssim_loss = ssim_loss(vis, ir, optimized_fused)
    # current_mi_loss = mi_loss(vis, ir, optimized_fused)
    # current_qabf_loss = qabf_loss(vis, ir, optimized_fused)
    # current_psnr_loss = psnr_loss(vis, ir, optimized_fused)
    # current_sf_loss = sf_loss(vis, ir, optimized_fused)
    # current_en_loss = en_loss(vis, ir, optimized_fused)



    # # combine loss 
    # current_combine_loss = (
    #                         current_ssim_loss    * w_ssim   + 
    #                         current_mi_loss      * w_mi     +
    #                         current_qabf_loss    * w_qabf   +
    #                         current_psnr_loss    * w_psnr   +
    #                         current_sf_loss      * w_sf     +
    #                         current_en_loss      * w_en
    #                                                              ) / (
                            
    #                         w_ssim + w_mi + w_qabf + w_psnr + w_sf + w_en
    #                                                             )
    
    # value of metrics
    current_en_val = en_val(vis, ir, optimized_fused)
    current_ce_val = ce_val(vis, ir, optimized_fused)
    current_mi_val = mi_val(vis, ir, optimized_fused)
    current_psnr_val = psnr_val(vis, ir, optimized_fused)
    current_ag_val = ag_val(vis, ir, optimized_fused)
    current_ei_val = ei_val(vis, ir, optimized_fused)
    current_qabf_val = qabf_val(vis, ir, optimized_fused)
    current_sd_val = sd_val(vis, ir, optimized_fused)
    current_sf_val = sf_val(vis, ir, optimized_fused)
    current_rmse_val = rmse_val(vis, ir, optimized_fused)
    current_ssim_val = ssim_val(vis, ir, optimized_fused)
    current_qcb_val = qcb_val(vis, ir, optimized_fused)
    
    # 反向传播优化
    optimizer.zero_grad()
    current_ssim_loss.backward()
    optimizer.step()

    # 保存 losses
    ssim_losses.append(current_ssim_loss.item())
    # mi_losses.append(current_mi_loss.item())
    # qabf_losses.append(current_qabf_loss.item())
    # psnr_losses.append(current_psnr_loss.item())
    # sf_losses.append(current_sf_loss.item())
    # en_losses.append(current_en_loss.item())
    # combine_losses.append(current_combine_loss.item())


    
    # 保存 metrics value
    
    ce_value.append(current_ce_val.item())
    en_value.append(current_en_val.item())
    mi_value.append(current_mi_val.item())
    ag_value.append(current_ag_val.item())
    ei_value.append(current_ei_val.item())
    qabf_value.append(current_qabf_val.item())
    sd_value.append(current_sd_val.item())
    sf_value.append(current_sf_val.item())
    rmse_value.append(current_rmse_val.item())
    ssim_value.append(current_ssim_val.item())
    qcb_value.append(current_qcb_val.item())

    
    # 打印优化进度
    print(f"Iteration {i}")
    print(f"SSIM_loss: {current_ssim_loss.item()}")
    print("\n")
    print(f"CE Value:{current_ce_val.item()}")
    print(f"EN Value:{current_en_val.item()}")
    print(f"MI Value:{current_mi_val.item()}")
    print(f"PSNR Value:{current_psnr_val.item()}")
    print(f"AG Value:{current_ag_val.item()}")
    print(f"EI Value:{current_ei_val.item()}")
    print(f"Qabf Value:{current_qabf_val.item()}")
    print(f"SD Value:{current_sd_val.item()}")
    print(f"SF Value:{current_sf_val.item()}")
    print(f"RMSE Value:{current_rmse_val.item()}")
    print(f"SSIM Value:{current_ssim_val.item()}")
    print(f"Qcb Value:{current_qcb_val.item()}")
    print("\n")

    # 保存优化图像
    optimized_image = optimized_fused.detach().clone().clamp(0, 1)
    optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
    optimized_image_display = optimized_image_display.astype(np.uint8)  # 将图像数据转换为uint8类型
    optimized_image_path = f"{output_folder}{method}_{scene}_{i}.png"
    os.makedirs(output_folder, exist_ok=True)
    TF.to_pil_image(optimized_image.squeeze(0).cpu()).save(optimized_image_path)

    # 显示图像
    original_subplt.clear()
    optimized_subplt.clear()

    original_subplt.imshow(fused.squeeze(0).cpu().numpy()[0], cmap='gray')
    optimized_subplt.imshow(optimized_image_display, cmap='gray')

    # 设置标题和关闭坐标轴
    original_subplt.set_title("Original Fused Image")
    optimized_subplt.set_title(f"Iteration: {i}")
    original_subplt.axis('off')
    optimized_subplt.axis('off')
    
    plt.pause(0.1)
    
    # 添加图像到视频
    writer.append_data(plt.imread(optimized_image_path))



# 关闭图像窗口
plt.close(fig)

# 关闭视频写入器
writer.close()
print(f"Video saved to {video_path}")


# 绘制SSIM Loss
plt.figure(figsize=(8, 5))
plt.plot(ssim_losses, label='SSIM Loss')
plt.title('SSIM Loss')
plt.xlabel('Iteration')
plt.ylabel('SSIM Loss')
plt.legend()


 
#——————————————#
# 绘制CE值曲线
plt.figure(figsize=(8, 5))
plt.plot(ce_value, label='CE Value')
plt.title('CE Value')
plt.xlabel('Iteration')
plt.ylabel('CE Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'ce_value.png'))
plt.close

# 绘制EN值曲线
plt.figure(figsize=(8, 5))
plt.plot(en_value, label='EN Value')
plt.title('EN Value')
plt.xlabel('Iteration')
plt.ylabel('EN Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'en_value.png'))

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(mi_value, label='MI Value')
plt.title('MI Value')
plt.xlabel('Iteration')
plt.ylabel('MI Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'mi_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(psnr_value, label='PSNR Value')
plt.title('PSNR Value')
plt.xlabel('Iteration')
plt.ylabel('PSNR Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'psnr_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(ag_value, label='AG Value')
plt.title('AG Value')
plt.xlabel('Iteration')
plt.ylabel('AG Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'ag_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(ei_value, label='EI Value')
plt.title('EI Value')
plt.xlabel('Iteration')
plt.ylabel('EI Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'ei_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(qabf_value, label='Qabf Value')
plt.title('Qabf Value')
plt.xlabel('Iteration')
plt.ylabel('Qabf Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'qabf_value.png'))
plt.close

# 绘制SD值曲线
plt.figure(figsize=(8, 5))
plt.plot(sd_value, label='SD Value')
plt.title('SD Value')
plt.xlabel('Iteration')
plt.ylabel('SD Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'sd_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(sf_value, label=' Value')
plt.title('SF Value')
plt.xlabel('Iteration')
plt.ylabel('SF Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'sf_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(rmse_value, label='RMSE Value')
plt.title('RMSE Value')
plt.xlabel('Iteration')
plt.ylabel('RMSE Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'rmse_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(ssim_value, label='SSIM Value')
plt.title('SSIM Value')
plt.xlabel('Iteration')
plt.ylabel('SSIM Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'ssim_value.png'))
plt.close

# 绘制ssim值曲线
plt.figure(figsize=(8, 5))
plt.plot(qcb_value, label='Qcb Value')
plt.title('Qcb Value')
plt.xlabel('Iteration')
plt.ylabel('Qcb Value')
plt.legend()
plt.savefig(os.path.join(output_folder, 'qcb_value.png'))
plt.close

