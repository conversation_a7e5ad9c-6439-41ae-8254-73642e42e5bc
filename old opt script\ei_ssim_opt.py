import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
import torch.nn.functional as F

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

vis_path = 'input/VIS/17.bmp'
ir_path = 'input/IR/17.bmp'
fused_path = 'input/Fused/17.png'

transform = transforms.Compose(
    [
        transforms.ToTensor(),
    ]
)

normalize = transforms.Normalize(mean=[0.5], std=[0.5])

vis = TF.to_tensor(Image.open(vis_path)).unsqueeze(0).to(device)
ir = TF.to_tensor(Image.open(ir_path)).unsqueeze(0).to(device)
fused = TF.to_tensor(Image.open(fused_path)).unsqueeze(0).to(device)

output_folder = "optimized_images/"

optimized_fused = nn.Parameter(fused.clone())


class Fusionloss(nn.Module):
    def __init__(self):
        super(Fusionloss, self).__init__()
        self.sobelconv = Sobelxy()

    def forward(self, image_vis, image_ir, generate_img):
        image_y = image_vis[:, :1, :, :]
        x_in_max = torch.max(image_y, image_ir)
        loss_in = F.l1_loss(x_in_max, generate_img)
        y_grad = self.sobelconv(image_y)
        ir_grad = self.sobelconv(image_ir)
        generate_img_grad = self.sobelconv(generate_img)
        x_grad_joint = torch.max(y_grad, ir_grad)
        loss_grad = F.l1_loss(x_grad_joint, generate_img_grad)
        loss_total = loss_in + 10 * loss_grad
        return loss_total, loss_in, loss_grad


class Sobelxy(nn.Module):
    def __init__(self):
        super(Sobelxy, self).__init__()
        kernelx = [[-1, 0, 1],
                   [-2, 0, 2],
                   [-1, 0, 1]]
        kernely = [[1, 2, 1],
                   [0, 0, 0],
                   [-1, -2, -1]]
        kernelx = torch.FloatTensor(kernelx).unsqueeze(0).unsqueeze(0)
        kernely = torch.FloatTensor(kernely).unsqueeze(0).unsqueeze(0)
        self.weightx = nn.Parameter(data=kernelx, requires_grad=False).to(device)
        self.weighty = nn.Parameter(data=kernely, requires_grad=False).to(device)

    def forward(self, x):
        sobelx = F.conv2d(x, self.weightx, padding=1)
        sobely = F.conv2d(x, self.weighty, padding=1)
        return torch.abs(sobelx) + torch.abs(sobely)


def ssim_loss(x, y):
    return kornia.losses.ssim_loss(x, y, window_size=11)


fusion_loss = Fusionloss().to(device)
optimizer = optim.Adam([optimized_fused], lr=0.00001)
num_iterations = 10000
fig = plt.figure(figsize=(6, 4), dpi=300)


original_subplt = plt.subplot(121)
optimized_subplt = plt.subplot(122)

video_path = 'output_video.mp4'
writer = imageio.get_writer(video_path, fps=10)

#ssim loss 权重
weight_ssim = 0.8

for i in range(num_iterations):

    loss_total, loss_in, loss_grad = fusion_loss(vis, ir, optimized_fused)
    ssim_loss_value = ssim_loss(optimized_fused, fused)


    #联合loss
    combined_loss = weight_ssim * ssim_loss_value + (1 - weight_ssim) * loss_total



    
    if i % 100 == 0:
        print(f"Iteration {i+100}, Combined Loss: {combined_loss.item()}, SSIM Loss: {ssim_loss_value.item()}, Edge Loss: {loss_total.item()}")

        
        optimized_image = optimized_fused.detach().clone().clamp(0, 1)
        optimized_image_path = f"{output_folder}optimized_image_iter_{i+100}.png"
        TF.to_pil_image(optimized_image.squeeze(0).cpu()).save(optimized_image_path)

        
        original_subplt.clear()
        optimized_subplt.clear()

        original_subplt.imshow(fused.squeeze(0).cpu().numpy()[0], cmap='gray')
        optimized_subplt.imshow(optimized_image.squeeze(0).cpu().numpy()[0], cmap='gray')

        original_subplt.set_title("Original Fused Image")
        optimized_subplt.set_title(f"Iteration: {i + 100}")
        original_subplt.axis('off')
        optimized_subplt.axis('off')

        plt.pause(0.1)

        
        writer.append_data(plt.imread(optimized_image_path))
        
        
        #优化
        optimizer.zero_grad()
        combined_loss.backward()
        optimizer.step()


plt.close(fig)


writer.close()
print(f"Video saved to {video_path}")
