#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行批量优化脚本
基于batch_optimization.py，支持多进程并行优化多张图像
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms.functional as TF
from PIL import Image
import os
import time
import numpy as np
import glob
from pathlib import Path
from tqdm import tqdm
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
from queue import Queue
import math

# 导入损失函数
from Losses import ssim
from Losses import psnr
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import vif
from Losses import scd
from Losses import mi
from Losses import cc
from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric

# 锁随机种子
seed = 42
torch.manual_seed(seed)
np.random.seed(seed)

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

class ProgressManager:
    """简化的进度管理器"""
    def __init__(self):
        self.lock = threading.Lock()

    def create_main_progress(self, total, desc="总体进度"):
        """创建主进度条"""
        with self.lock:
            self.main_progress = tqdm(total=total, desc=desc, position=0, leave=True)
            return self.main_progress

    def create_batch_progress(self, batch_id, total, desc="批次进度"):
        """创建批次进度条"""
        with self.lock:
            return tqdm(total=total, desc=f"{desc} {batch_id+1}", position=1, leave=False)

    def close_all(self):
        """关闭所有进度条"""
        with self.lock:
            if hasattr(self, 'main_progress'):
                self.main_progress.close()

class ParallelBatchOptimizer:
    def __init__(self, algorithm_name, lr=0.003, num_iterations=200, max_workers=None):
        self.algorithm_name = algorithm_name
        self.lr = lr
        self.num_iterations = num_iterations
        
        # 设置并行工作进程数
        if max_workers is None:
            self.max_workers = min(mp.cpu_count(), 4)  # 默认最多4个进程
        else:
            self.max_workers = max_workers
        
        # 设置路径
        self.ir_base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\IR4val"
        self.vis_base_path = r"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\VIS4val"
        self.fused_base_path = rf"F:\.Infrared and Visiable Light Fusion\Optimization based\input4table\Fused\{algorithm_name}"
        self.output_base_path = rf"F:\.Infrared and Visiable Light Fusion\Optimization based\output\{algorithm_name}"
        
        # 创建输出目录
        os.makedirs(self.output_base_path, exist_ok=True)
        
        print(f"算法: {algorithm_name}")
        print(f"并行进程数: {self.max_workers}")
        print(f"红外图路径: {self.ir_base_path}")
        print(f"可见光图路径: {self.vis_base_path}")
        print(f"融合图路径: {self.fused_base_path}")
        print(f"输出路径: {self.output_base_path}")
        print(f"学习率: {lr}, 迭代次数: {num_iterations}")
    
    def find_matching_images(self, skip_existing=True):
        """查找所有匹配的图像组合"""
        # 获取融合图像目录中的所有图像文件
        fusion_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']:
            fusion_files.extend(glob.glob(os.path.join(self.fused_base_path, ext)))

        matching_files = []
        skipped_files = []

        for fusion_path in fusion_files:
            # 获取文件名（不含扩展名）
            filename = Path(fusion_path).stem

            # 构建对应的红外和可见光图像路径
            ir_path = os.path.join(self.ir_base_path, f"{filename}.png")
            vis_path = os.path.join(self.vis_base_path, f"{filename}.png")
            output_path = os.path.join(self.output_base_path, f"{filename}.png")

            # 检查文件是否存在
            if os.path.exists(ir_path) and os.path.exists(vis_path):
                # 检查输出文件是否已存在
                if skip_existing and os.path.exists(output_path):
                    skipped_files.append(filename)
                    print(f"跳过已存在的文件: {filename}")
                else:
                    matching_files.append({
                        'filename': filename,
                        'ir_path': ir_path,
                        'vis_path': vis_path,
                        'fusion_path': fusion_path,
                        'output_path': output_path
                    })
            else:
                print(f"警告: 找不到匹配的文件 {filename}")

        if skipped_files:
            print(f"跳过了 {len(skipped_files)} 个已存在的文件")

        return matching_files, skipped_files

    def check_existing_files(self):
        """检查输出目录中已存在的文件"""
        existing_files = []
        if os.path.exists(self.output_base_path):
            for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']:
                existing_files.extend(glob.glob(os.path.join(self.output_base_path, ext)))

        existing_filenames = [Path(f).stem for f in existing_files]
        return existing_files, existing_filenames

    def get_file_processing_mode(self):
        """获取文件处理模式"""
        existing_files, existing_filenames = self.check_existing_files()

        if not existing_files:
            print("输出目录为空，将处理所有文件")
            return 'all', []

        print(f"\n发现输出目录中已有 {len(existing_files)} 个文件:")
        for i, filename in enumerate(existing_filenames[:10]):  # 只显示前10个
            print(f"  {i+1}. {filename}")
        if len(existing_filenames) > 10:
            print(f"  ... 还有 {len(existing_filenames) - 10} 个文件")

        print("\n请选择处理模式:")
        print("1. 跳过已存在的文件，只处理新文件 (推荐)")
        print("2. 重新处理所有文件 (覆盖已存在的文件)")
        print("3. 退出程序")

        while True:
            choice = input("请输入选择 (1/2/3): ").strip()
            if choice == '1':
                return 'skip_existing', existing_filenames
            elif choice == '2':
                confirm = input("确认要覆盖所有已存在的文件吗? (y/N): ").strip().lower()
                if confirm == 'y':
                    return 'overwrite_all', existing_filenames
                else:
                    print("已取消，请重新选择")
            elif choice == '3':
                return 'exit', existing_filenames
            else:
                print("无效选择，请输入 1、2 或 3")
    
    def run_batch_optimization(self):
        """运行并行批量优化"""
        print("=== 并行批量优化开始 ===")

        # 检查路径是否存在
        if not os.path.exists(self.fused_base_path):
            print(f"错误: 融合图路径不存在: {self.fused_base_path}")
            return

        if not os.path.exists(self.ir_base_path):
            print(f"错误: 红外图路径不存在: {self.ir_base_path}")
            return

        if not os.path.exists(self.vis_base_path):
            print(f"错误: 可见光图路径不存在: {self.vis_base_path}")
            return

        # 获取文件处理模式
        processing_mode, existing_filenames = self.get_file_processing_mode()

        if processing_mode == 'exit':
            print("程序已退出")
            return

        # 根据处理模式查找匹配的图像
        skip_existing = (processing_mode == 'skip_existing')
        matching_files, skipped_files = self.find_matching_images(skip_existing)

        if not matching_files:
            if skipped_files:
                print("所有文件都已存在，无需重新优化")
            else:
                print("没有找到匹配的图像文件")
            return

        print(f"\n找到 {len(matching_files)} 组需要处理的图像")
        if skipped_files:
            print(f"跳过 {len(skipped_files)} 个已存在的文件")
        print(f"使用 {self.max_workers} 个并行进程")

        # 显示处理模式信息
        if processing_mode == 'skip_existing':
            print("模式: 跳过已存在的文件")
        elif processing_mode == 'overwrite_all':
            print("模式: 覆盖所有文件")
        else:
            print("模式: 处理所有文件")

        # 将图像分批处理
        batch_size = self.max_workers
        batches = [matching_files[i:i + batch_size] for i in range(0, len(matching_files), batch_size)]

        print(f"分为 {len(batches)} 个批次，每批最多 {batch_size} 张图像")

        # 创建进度管理器
        progress_manager = ProgressManager()
        main_progress = progress_manager.create_main_progress(len(matching_files), "总体进度")

        # 并行优化
        start_time = time.time()
        success_count = 0
        failed_files = []

        try:
            for batch_idx, batch_files in enumerate(batches):
                print(f"\n处理第 {batch_idx + 1}/{len(batches)} 批次 ({len(batch_files)} 张图像)")

                # 创建批次进度条
                batch_progress = progress_manager.create_batch_progress(
                    batch_idx, len(batch_files), "批次"
                )

                # 使用ProcessPoolExecutor进行并行处理
                with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                    # 提交当前批次的所有任务
                    future_to_file = {
                        executor.submit(
                            optimize_single_image_worker,
                            file_info,
                            self.lr,
                            self.num_iterations,
                            batch_idx
                        ): file_info for file_info in batch_files
                    }

                    # 处理完成的任务
                    for future in as_completed(future_to_file):
                        file_info = future_to_file[future]
                        try:
                            result = future.result()
                            if result['success']:
                                success_count += 1
                                self.print_metrics_comparison(
                                    result['filename'],
                                    result['original_metrics'],
                                    result['final_metrics']
                                )
                            else:
                                failed_files.append(file_info['filename'])
                                print(f"优化失败: {file_info['filename']}")
                        except Exception as e:
                            failed_files.append(file_info['filename'])
                            print(f"处理 {file_info['filename']} 时出错: {e}")

                        # 更新进度条
                        batch_progress.update(1)
                        main_progress.update(1)

                # 关闭批次进度条
                batch_progress.close()

        finally:
            # 确保所有进度条都被关闭
            progress_manager.close_all()

        # 总结
        total_time = time.time() - start_time
        total_processed = len(matching_files)
        total_skipped = len(skipped_files) if skipped_files else 0
        total_images = total_processed + total_skipped

        print(f"\n{'='*50}")
        print("=== 并行批量优化完成 ===")
        print(f"总图像数: {total_images}")
        print(f"处理图像数: {total_processed}")
        if total_skipped > 0:
            print(f"跳过图像数: {total_skipped}")
        print(f"成功优化: {success_count}")
        print(f"失败数量: {len(failed_files)}")
        if failed_files:
            print(f"失败文件: {', '.join(failed_files)}")
        print(f"总耗时: {total_time:.2f} 秒")
        if total_processed > 0:
            print(f"平均每张图像: {total_time/total_processed:.2f} 秒")
            print(f"并行加速比: {total_processed * (total_time/total_processed) / total_time:.2f}x")
        print(f"输出目录: {self.output_base_path}")

        # 显示处理模式总结
        if processing_mode == 'skip_existing':
            print(f"处理模式: 跳过已存在文件 (节省了 {total_skipped} 个文件的处理时间)")
        elif processing_mode == 'overwrite_all':
            print("处理模式: 覆盖所有文件")
        else:
            print("处理模式: 处理所有文件")
    
    def print_metrics_comparison(self, filename, original_metrics, final_metrics):
        """打印指标对比结果"""
        print(f"\n=== {filename} 指标对比 ===")
        print(f"{'指标':<8} {'原始值':<12} {'优化值':<12} {'变化':<12} {'变化率':<12}")
        print("-" * 60)
        
        for metric_name in original_metrics.keys():
            orig_val = original_metrics[metric_name]
            final_val = final_metrics[metric_name]
            diff = final_val - orig_val
            
            # 计算变化百分比
            if metric_name == 'CE':  # CE越小越好
                change_percent = -((final_val - orig_val) / orig_val) * 100 if orig_val != 0 else 0
            else:  # 其他指标越大越好
                change_percent = ((final_val - orig_val) / orig_val) * 100 if orig_val != 0 else 0
            
            print(f"{metric_name:<8} {orig_val:<12.4f} {final_val:<12.4f} {diff:<12.4f} {change_percent:<12.2f}%")


def optimize_single_image_worker(file_info, lr, num_iterations, batch_idx):
    """
    单个图像优化的工作函数（用于多进程）
    这个函数会在独立的进程中运行
    """
    try:
        # 在每个进程中设置设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 设置随机种子
        torch.manual_seed(42)
        np.random.seed(42)
        
        filename = file_info['filename']
        
        # 读取图像
        def read_image(path):
            try:
                img = TF.to_tensor(Image.open(path)).unsqueeze(0).to(device)
                if img.shape[1] == 3:
                    img = img.mean(dim=1, keepdim=True)
                return img
            except Exception as e:
                print(f"读取图像失败: {path}, 错误: {e}")
                return None
        
        vis = read_image(file_info['vis_path'])
        ir = read_image(file_info['ir_path'])
        fused = read_image(file_info['fusion_path'])
        
        if vis is None or ir is None or fused is None:
            return {'success': False, 'filename': filename, 'error': '图像读取失败'}
        
        # 计算指标函数
        def calculate_metrics_absolute(vis, ir, fused):
            metrics = {}
            try:
                metrics['CE'] = ce_metric(vis, ir, fused).item()
                metrics['EN'] = en_metric(vis, ir, fused).item()
                metrics['MI'] = mi_metric(vis, ir, fused).item()
                metrics['PSNR'] = psnr_metric(vis, ir, fused).item()
                metrics['Qabf'] = q_abf_metric(vis, ir, fused).item()
                metrics['SD'] = sd_metric(vis, ir, fused).item()
                metrics['SF'] = sf_metric(vis, ir, fused).item()
                metrics['SSIM'] = ssim_metric(vis, ir, fused).item()
                metrics['VIF'] = vif_metric(vis, ir, fused).item()
                metrics['SCD'] = scd_metric(vis, ir, fused).item()
                metrics['CC'] = cc_metric(vis, ir, fused).item()
            except Exception as e:
                for metric in ['CE', 'EN', 'MI', 'PSNR', 'Qabf', 'SD', 'SF', 'SSIM', 'VIF', 'SCD', 'CC']:
                    metrics[metric] = 0.0
            return metrics
        
        # 计算原始指标
        original_metrics = calculate_metrics_absolute(vis, ir, fused)
        
        # 损失函数
        def monotone_increasing_loss(x, y, vis, ir):
            ssim_diff = (ssim.ssim_metric(vis, ir, y) - ssim.ssim_metric(vis, ir, x)) / ssim.ssim_metric(vis, ir, x) 
            mi_diff = (mi.mi_metric(vis, ir, y) - mi.mi_metric(vis, ir, x)) / mi.mi_metric(vis, ir, x) 
            qabf_diff = (qabf.q_abf_metric(vis, ir, y) - qabf.q_abf_metric(vis, ir, x)) / qabf.q_abf_metric(vis, ir, x) 
            psnr_diff = (psnr.psnr_metric(vis, ir, y) - psnr.psnr_metric(vis, ir, x)) / psnr.psnr_metric(vis, ir, x) 
            sf_diff = (sf.sf_metric(vis, ir, y) - sf.sf_metric(vis, ir, x)) / sf.sf_metric(vis, ir, x) 
            sd_diff = (sd.sd_metric(vis, ir, y) - sd.sd_metric(vis, ir, x)) / sd.sd_metric(vis, ir, x)
            vif_diff = (vif.vif_metric(vis, ir, y) - vif.vif_metric(vis, ir, x)) / vif.vif_metric(vis, ir, x)
            scd_diff = (scd.scd_metric(vis, ir, y) - scd.scd_metric(vis, ir, x)) / scd.scd_metric(vis, ir, x)
            cc_diff = (cc.cc_metric(vis, ir, y) - cc.cc_metric(vis, ir, x)) / cc.cc_metric(vis, ir, x)

            def mapping_function(diff):
                mapped_diff = (torch.exp(5.5 * diff) - torch.exp(5.5 * -diff))/ (torch.exp(5.5 * diff) + torch.exp(5.5 * -diff))
                return mapped_diff

            ssim_mapped = mapping_function(ssim_diff)
            mi_mapped = mapping_function(mi_diff)
            qabf_mapped = mapping_function(qabf_diff)
            psnr_mapped = mapping_function(psnr_diff)
            sf_mapped = mapping_function(sf_diff)
            sd_mapped = mapping_function(sd_diff)
            vif_mapped = mapping_function(vif_diff)
            scd_mapped = mapping_function(scd_diff)
            cc_mapped = mapping_function(cc_diff)

            loss = ( 
                ssim_mapped +
                mi_mapped * 0.01 +  
                qabf_mapped * 0.5 + 
                sf_mapped * 0.1 +
                vif_mapped +
                scd_mapped +
                cc_mapped  
            ) / 7
            
            return loss
        
        # 初始化优化参数
        optimized_fused = nn.Parameter(fused.clone()).to(device)
        optimizer = optim.Adam([optimized_fused], lr)

        # 优化循环
        print(f"开始优化 {filename}...")
        for i in range(num_iterations + 1):
            current_loss = monotone_increasing_loss(optimized_fused, fused, vis, ir)
            optimizer.zero_grad()
            current_loss.backward()
            optimizer.step()

            # 每50次迭代打印一次进度
            if i % 50 == 0 or i == num_iterations:
                progress_percent = (i / num_iterations) * 100
                print(f"  {filename}: {i}/{num_iterations} ({progress_percent:.1f}%) - Loss: {current_loss.item():.6f}")

        print(f"完成优化 {filename}")
        
        # 计算优化后指标
        final_metrics = calculate_metrics_absolute(vis, ir, optimized_fused)
        
        # 保存优化后的图像
        final_optimized_image = optimized_fused.detach().clone().clamp(0, 1)
        TF.to_pil_image(final_optimized_image.squeeze(0).cpu()).save(file_info['output_path'])
        
        return {
            'success': True,
            'filename': filename,
            'original_metrics': original_metrics,
            'final_metrics': final_metrics
        }
        
    except Exception as e:
        return {'success': False, 'filename': file_info['filename'], 'error': str(e)}


if __name__ == "__main__":
    # 配置参数
    algorithm_name = "TarDAL_val"  # 可修改的算法名称
    learning_rate = 0.003   # 学习率
    iterations = 200        # 迭代次数
    max_workers = 3       # 最大并行进程数，可根据CPU核心数调整
    
    # 创建并行批量优化器并运行
    optimizer = ParallelBatchOptimizer(algorithm_name, learning_rate, iterations, max_workers)
    optimizer.run_batch_optimization()
