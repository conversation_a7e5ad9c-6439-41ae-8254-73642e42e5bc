import cv2
import torchvision.transforms.functional as TF
from PIL import Image
from Losses import ssim
from Losses import mi
from Losses import ei
from Losses import psnr
from Losses import en
from Losses import ce
from Losses import ag
from Losses import rmse
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import q_cb
from Losses import vif




method = 'ADF'
scene = 'jeep'
metric = 'mono'
i = 100
vis_path = f'input/VIS/{scene}.bmp'
ir_path = f'input/IR/{scene}.bmp'
fused_path = f'input/Fused/{scene}_{method}.png'
fused_opt_path = f'../vgg-tensorflow-master/images/{scene}/{metric}/{method}/{method}_{scene}_{i}.png'

# 读取图像并转化为灰度图像
def read_image(path):
    image = TF.to_tensor(Image.open(path)).unsqueeze(0)
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    return image

# 计算指标
def calculate_metrics(vis, ir, fused):
    metrics = {}
    metrics['CE'] = ce.ce_metric(vis, ir, fused)
    metrics['EN'] = en.en_metric(vis, ir, fused)
    metrics['MI'] = mi.mi_metric(vis, ir, fused)
    metrics['AG'] = ag.ag_metric(vis, ir, fused)
    metrics['EI'] = ei.ei_metric(vis, ir, fused)
    metrics['PSNR'] = psnr.psnr_metric(vis, ir, fused)
    metrics['Q_ABF'] = qabf.q_abf_metric(vis, ir, fused)
    metrics['SD'] = sd.sd_metric(vis, ir, fused)
    metrics['SF'] = sf.sf_metric(vis, ir, fused)
    metrics['RMSE'] = rmse.rmse_metric(vis, ir, fused)
    metrics['SSIM'] = ssim.ssim_metric(vis, ir, fused)
    return metrics

# 计算优化后的指标
vis = read_image(vis_path)
ir = read_image(ir_path)
fused = read_image(fused_path)
fused_opt = read_image(fused_opt_path)

metrics_fused = calculate_metrics(vis, ir, fused)
metrics_fused_opt = calculate_metrics(vis, ir, fused_opt)

# 计算指标涨幅
improvements = {}
for metric_name, metric_value in metrics_fused.items():
    improvements[metric_name] = ((metrics_fused_opt[metric_name] - metric_value) / metric_value) * 100

# 打印指标值
print("原始融合图指标:")
for metric_name, metric_value in metrics_fused.items():
    print(f"{metric_name}: {metric_value}")

print("\n优化后融合图指标:")
for metric_name, metric_value in metrics_fused_opt.items():
    print(f"{metric_name}: {metric_value}")

print("\n涨降幅百分比:")
for metric_name, improvement in improvements.items():
    print(f"{metric_name}: {improvement:.2f}%")
