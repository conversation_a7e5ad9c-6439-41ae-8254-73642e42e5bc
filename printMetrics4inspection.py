import cv2
import torchvision.transforms.functional as TF
from PIL import Image
import numpy as np
import torch
from Losses import ce_metric
from Losses import en_metric
from Losses import mi_metric
from Losses import ag_metric
from Losses import ei_metric
from Losses import psnr_metric
from Losses import q_abf_metric
from Losses import sd_metric
from Losses import rmse_metric
from Losses import ssim_metric
from Losses import sf_metric
from Losses import q_cb_metric
from Losses import scd_metric
from Losses import vif_metric
from Losses import cc_metric



def read_image(path):
    image = TF.to_tensor(Image.open(path)).unsqueeze(0)
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    return image.cuda()  


def calculate_metrics(vis, ir, fused):
    metrics = {}
    for metric_name, metric_func in metrics_functions.items():
        metrics[metric_name] = metric_func(vis, ir, fused).cuda()  
    return metrics

# 计算指标涨幅
def calculate_improvement(metric_before, metric_after, metric_name):
    if metric_name in ['CE']:
        # 对于越小越好的指标，涨幅百分比应该是负的，如果降低了则是正的
        return -((metric_after - metric_before) / metric_before) * 100
    else:
        if metric_before == 0:
            return 0
        # 对于越大越好的指标，涨幅百分比应该是正的，如果提高了则是正的
        return ((metric_after - metric_before) / metric_before) * 100
    
metrics_functions = {
    # 'CE': ce_metric,
    'EN': en_metric,
    'MI': mi_metric, 
    # 'PSNR': psnr_metric,
    # 'AG': ag_metric,
    # 'EI': ei_metric,
    'Qabf': q_abf_metric,
    'SD': sd_metric,
    'SF': sf_metric,
    # 'RMSE': rmse_metric,
    'SSIM': ssim_metric,
    # 'Qcb': q_cb_metric,
    'VIF': vif_metric,
    'SCD': scd_metric,
    'CC': cc_metric
}
    
    