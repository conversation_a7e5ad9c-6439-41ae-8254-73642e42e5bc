import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
from torch.nn.functional import mse_loss as mse
import numpy as np
import torch.nn.functional as F
from Losses import psnr_loss


os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

img1_path = 'input/VIS/jeep.bmp'
img2_path = 'input/IR/jeep.bmp'
fused_path = 'input/Fused/jeep_CBF.png'

transform = transforms.Compose(
    [
        transforms.ToTensor(),
    ]
)

img1 = TF.to_tensor(Image.open(img1_path)).unsqueeze(0).to(device)
img2 = TF.to_tensor(Image.open(img2_path)).unsqueeze(0).to(device)
fused = TF.to_tensor(Image.open(fused_path)).unsqueeze(0).to(device)

output_folder = "images_saved/PSNR"

# 将要优化的参数初始化为融合图像
optimized_fused = nn.Parameter(fused.clone())

# def psnr_loss(x, y, z):
#     # 计算每一对图像的 MSE
#     mse_ir = mse(x, z)
#     mse_vis = mse(y, z)
    
#     # 计算均值 MSE
#     mean_mse = (mse_ir + mse_vis) / 2.0
    
#     # 使用均值 MSE 计算 PSNR
#     return -20 * torch.log10(255**2 / mean_mse)

# 定义优化器
optimizer = optim.Adam([optimized_fused], lr=0.00001)

# 训练迭代次数
num_iterations = 10000

# 设置图像窗口的大小
fig = plt.figure(figsize=(6, 4), dpi=300)

# 两个子图的初始状态
original_subplt = plt.subplot(121)
optimized_subplt = plt.subplot(122)

# 添加视频录制功能
video_path = 'output_video.mp4'
writer = imageio.get_writer(video_path, fps=10)

for i in range(num_iterations):
    # 计算psnr损失
    current_psnr_loss = psnr_loss(img1, img2, optimized_fused)

    # 反向传播优化
    optimizer.zero_grad()
    current_psnr_loss.backward()
    optimizer.step()

    # 打印优化进度
    if i % 100 == 0:
        print(f"Iteration {i}, psnr_loss: {current_psnr_loss.item()}")

        # 保存优化图像
        optimized_image = optimized_fused.detach().clone().clamp(0, 1)
        optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
        optimized_image_path = f"{output_folder}optimized_image_iter_{i + 100}.png"
        TF.to_pil_image(optimized_image.clamp(0, 1).squeeze(0).cpu()).save(optimized_image_path)

        # 显示图像
        original_subplt.clear()
        optimized_subplt.clear()

        original_subplt.imshow(fused.squeeze(0).cpu().numpy()[0], cmap='gray')
        optimized_subplt.imshow(optimized_image_display, cmap='gray')

        # 设置标题和关闭坐标轴
        original_subplt.set_title("Original Fused Image")
        optimized_subplt.set_title(f"Iteration: {i}")
        original_subplt.axis('off')
        optimized_subplt.axis('off')

        plt.pause(0.1)
        
        # 添加图像到视频
        writer.append_data(plt.imread(optimized_image_path))
        

    
        


# 关闭图像窗口
plt.close(fig)

# 关闭视频写入器
writer.close()
print(f"Video saved to {video_path}")
