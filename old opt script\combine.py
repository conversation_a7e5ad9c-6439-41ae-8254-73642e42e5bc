import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import imageio
import numpy as np
from torch.nn.functional import mse_loss as mse
from Losses import qabf
from Losses import psnr
from Losses import ag
from Losses import ei
from Losses import mi

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'


class ImageFusionOptimizer:
    def __init__(self, method, scene, metric, vis_path, ir_path, fused_path, output_folder, display_interval=100):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.method = method
        self.scene = scene
        self.metric = metric
        self.vis_path = vis_path
        self.ir_path = ir_path
        self.fused_path = fused_path
        self.output_folder = output_folder
        self.display_interval = display_interval
        self.lr = lr
        self.num_iterations = num_iterations
        
        self.transform = transforms.Compose([transforms.ToTensor()])

        self.vis = self._process_image(Image.open(self.vis_path))
        self.ir = self._process_image(Image.open(self.ir_path))
        self.fused = self._process_image(Image.open(self.fused_path))

        self._check_channel_dimensions()

        self.optimized_fused = nn.Parameter(self.fused.clone())
        self.optimizer = optim.Adam([self.optimized_fused], self.lr)

        self.loss_weights = {'ssim': 2.0, 'qabf': 1.0, 'psnr': 0.05, 'ei': 0.0005, 'ag': 0.0, 'mi': 1.0}



        # 设置图像窗口的大小
        self.fig = plt.figure(figsize=(6, 4), dpi=300)

        # 两个子图的初始状态
        self.original_subplt = plt.subplot(121)
        self.optimized_subplt = plt.subplot(122)

        self.losses_ssim,  self.losses_qabf, self.losses_combine, self.losses_psnr, self.losses_ei, self.losses_ag, self.losses_mi= [], [], [], [], [], [], []
        self.ssim_val, self.mi_val = [], []

    def _process_image(self, image):
        image_tensor = TF.to_tensor(image).unsqueeze(0).to(self.device)
        if image_tensor.shape[1] == 3:
            image_tensor = image_tensor.mean(dim=1, keepdim=True)
        return image_tensor

    def _check_channel_dimensions(self):
        if self.vis.shape[1] != 1 or self.ir.shape[1] != 1 or self.fused.shape[1] != 1:
            raise ValueError("Input images must have a single channel.")


    def ssim_loss(self, x, y, z):
        ssim_loss_vis = kornia.losses.ssim_loss(x, z, window_size=11)
        ssim_loss_ir = kornia.losses.ssim_loss(y, z, window_size=11)
        ssim_loss_total = (ssim_loss_vis + ssim_loss_ir) / 2
        return ssim_loss_total

    def psnr_loss(self, x, y, z):
        return psnr.psnr_approach_loss(x,y,z)

    def qabf_loss(self, x, y, z):
        vis = qabf.q_abf_approach_loss(x,z)
        ir = qabf.q_abf_approach_loss(y,z)
        return (vis + ir ) / 2
    
    def average_gradient_loss(self, x,y,z):
        vis = ag.ag_approach_loss(x,z)
        ir = ag.ag_approach_loss(y,z)
        return (vis + ir) /2
    
    def edge_intensity_loss(self, x, y,z ):
        vis = ei.ei_approach_loss(x,z)
        ir = ei.ei_approach_loss(y,z)
        return (vis + ir) /2
    
    def mi_loss(self, x, y, z ):
        mi_loss_vis = mi.mi_differentiable_loss(x,z)
        mi_loss_ir = mi.mi_differentiable_loss(y,z)
        mi_loss_total = mi_loss_ir + mi_loss_vis
        return mi_loss_total
    

    

    def calculate_losses(self):

        current_ssim_loss = self.ssim_loss(self.vis, self.ir, self.optimized_fused)
        current_qabf_loss = self.qabf_loss(self.vis, self.ir, self.optimized_fused)
        current_psnr_loss = self.psnr_loss(self.vis, self.ir, self.optimized_fused)
        current_ag_loss = self.average_gradient_loss(self.optimized_fused)
        current_ei_loss = self.edge_intensity_loss(self.optimized_fused)
        current_mi_loss = self.mi_loss(self.vis, self.ir, self.optimized_fused)

        combine_loss = (self.loss_weights['ssim']    * current_ssim_loss +
                        self.loss_weights['qabf']    * current_qabf_loss +
                        self.loss_weights['psnr']    * current_psnr_loss +
                        self.loss_weights['ei']      * current_ei_loss   +
                        # self.loss_weights['ag']      * current_ag_loss   +
                        self.loss_weights['mi']      * current_mi_loss
                        ) / (self.loss_weights['ssim'] + 
                             self.loss_weights['qabf'] + 
                             self.loss_weights['psnr'] + 
                             self.loss_weights['ei']   +
                            #  self.loss_weights['ag']   +
                             self.loss_weights['mi'])  
                             

        return current_ssim_loss, current_qabf_loss, combine_loss, current_psnr_loss, current_ei_loss, current_ag_loss, current_mi_loss

    def train(self):
        # Initialize the video writer
        video_path = f'{self.method}_{self.scene}_{self.metric}.mp4'
        writer = imageio.get_writer(video_path, fps=10)

        for i in range(self.num_iterations):
            ssim_loss, qabf_loss, combine_loss, psnr_loss, ei_loss, ag_loss, mi_loss  = self.calculate_losses()

            self.optimizer.zero_grad()
            combine_loss.backward()
            self.optimizer.step()

            self.losses_ssim.append(ssim_loss.item())
            self.losses_qabf.append(qabf_loss.item())
            self.losses_psnr.append(psnr_loss.item())
            # self.losses_ag.append(ag_loss.item())
            self.losses_ei.append(ei_loss.item())
            self.losses_mi.append(mi_loss.item())
            self.losses_combine.append(combine_loss.item())

            if i % self.display_interval == 0:
                print(f"Iteration {i + 100}")
                print(f"SSIM_loss: {ssim_loss.item()}")
                print(f"qabf_loss: {qabf_loss.item()}")
                print(f"psnr_loss: {psnr_loss.item()}")
                print(f"ei_loss: {ei_loss.item()}")
                # print(f"ag_loss: {ag_loss.item()}")
                print(f"mi_loss: {mi_loss.item()}")
                print(f"Combine_loss: {combine_loss.item()}")
                print('\n')

                optimized_image = self.optimized_fused.detach().clone().clamp(0, 1)
                optimized_image_display = optimized_image.clone().squeeze(0).cpu().numpy()[0] * 255
                optimized_image_display = optimized_image_display.astype(np.uint8)
                optimized_image_path = f"{self.output_folder}{self.method}_{self.scene}_{self.metric}_{i + 100}.png"

                # 创建文件夹（如果不存在）
                os.makedirs(self.output_folder, exist_ok=True)

                optimized_pil_image = TF.to_pil_image(optimized_image.squeeze(0).cpu())
                optimized_pil_image.save(optimized_image_path)

                # 显示图像
                self.original_subplt.clear()
                self.optimized_subplt.clear()

                self.original_subplt.imshow(self.fused.squeeze(0).cpu().numpy()[0], cmap='gray')
                self.optimized_subplt.imshow(optimized_image_display, cmap='gray')

                # 设置标题和关闭坐标轴
                self.original_subplt.set_title("Original Fused Image")
                self.optimized_subplt.set_title(f"Iteration: {i + 100}")
                self.original_subplt.axis('off')
                self.optimized_subplt.axis('off')

                # 刷新图像
                plt.pause(0.1)

                # 添加图像到视频
                writer.append_data(plt.imread(optimized_image_path))

        # 关闭图像窗口
        plt.close(self.fig)

        # 关闭视频写入器
        writer.close()
        print(f"Video saved to {video_path}")

    def plot_losses(self):
        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_ssim, label=f'SSIM Loss')
        plt.title(f'SSIM Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'SSIM Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_ssim.png'))
        plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_qabf, label=f'Qabf Loss')
        plt.title(f'Qabf Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'Qabf Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_qabf.png'))
        plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_qabf, label=f'psnr Loss')
        plt.title(f'psnr Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'psnr Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_psnr.png'))
        plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_ei, label=f'ei Loss')
        plt.title(f'ei Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'ei Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_ei.png'))
        plt.close

        # plt.figure(figsize=(8, 5))
        # plt.plot(self.losses_ag, label=f'ag Loss')
        # plt.title(f'ag Loss ')
        # plt.xlabel('Iteration')
        # plt.ylabel(f'ag Loss')
        # plt.legend()
        # plt.savefig(os.path.join(output_folder, 'loss_ag.png'))
        # plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_ag, label=f'mi Loss')
        plt.title(f'mi Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'mi Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_mi.png'))
        plt.close

        plt.figure(figsize=(8, 5))
        plt.plot(self.losses_combine, label=f'Combine Loss')
        plt.title(f'Combine Loss ')
        plt.xlabel('Iteration')
        plt.ylabel(f'Combine Loss')
        plt.legend()
        plt.savefig(os.path.join(output_folder, 'loss_combine.png'))
        plt.close
        # plt.figure(figsize=(8, 5))
        # plt.plot(self.mi_val, label=f'{self.metric} Value')
        # plt.title(f'{self.metric} Value')
        # plt.xlabel('Iteration')
        # plt.ylabel(f'{self.metric} Value')
        # plt.legend()

        plt.show()


# 使用 ImageFusionOptimizer 类
method = 'ADF'
lr = 0.001
num_iterations = 5000
scene = 'building'
metric = 'Combine'
vis_path = f'input/VIS/{scene}.png'
ir_path = f'input/IR/{scene}.png'
fused_path = f'input/Fused/{scene}_{method}.png'
output_folder = f"../vgg-tensorflow-master/images/{scene}/{metric}/{method}/"

fusion_optimizer = ImageFusionOptimizer(method, scene, metric, vis_path, ir_path, fused_path, output_folder)
fusion_optimizer.train()
fusion_optimizer.plot_losses()
