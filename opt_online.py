import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms
from torch.autograd import Variable
import matplotlib.pyplot as plt
from PIL import Image
import kornia
import torchvision.transforms.functional as TF
import os
import time
import numpy as np
import pandas as pd
from Losses import ssim
from Losses import psnr
from Losses import sf
from Losses import qabf
from Losses import sd
from Losses import vif
from Losses import scd
from Losses import mi
from Losses import cc
from Losses import ce_metric, en_metric, mi_metric, psnr_metric, q_abf_metric, sd_metric, sf_metric, ssim_metric, vif_metric, scd_metric, cc_metric, ei_metric
from PIL import Image

# 锁随机种子
seed = 42
torch.manual_seed(seed)
np.random.seed(seed)

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

method = 'ADF_val'
scene = '00280'
metric = 'lr0.003'

vis_path = f'input4table/VIS4val/{scene}.png'
ir_path = f'input4table/IR4val/{scene}.png'
# fused_path = f'input4table/Fused/{method}/{scene}_{method}.png'
fused_path = f'input4table/Fused/{method}/{scene}.png'
    

# Set hyperparameters
lr = 0.003
num_iterations = 200

transform = transforms.Compose(
    [
        transforms.ToTensor(),
    ]
)

vis = TF.to_tensor(Image.open(vis_path)).unsqueeze(0).to(device)
ir = TF.to_tensor(Image.open(ir_path)).unsqueeze(0).to(device)
fused = TF.to_tensor(Image.open(fused_path)).unsqueeze(0).to(device)

output_folder = f"./output/{scene}/{metric}/{method}/"

# 三通道变单通道
if vis.shape[1] == 3:
    vis = vis.mean(dim=1, keepdim=True)
if ir.shape[1] == 3:
    ir = ir.mean(dim=1, keepdim=True)
if fused.shape[1] == 3:
    fused = fused.mean(dim=1, keepdim=True)



optimized_fused = nn.Parameter(fused.clone()).to(device)

# 读取图像并转化为灰度图像
def read_image_for_metrics(path):
    image = TF.to_tensor(Image.open(path)).unsqueeze(0)
    if len(image.shape) == 3:
        image = image.mean(dim=1, keepdim=True)
    return image.cuda()

# 计算指标绝对值
def calculate_metrics_absolute(vis, ir, fused):
    metrics = {}
    metrics['CE'] = ce_metric(vis, ir, fused).item()
    metrics['EI'] = ei_metric(vis, ir, fused).item()
    metrics['EN'] = en_metric(vis, ir, fused).item()
    metrics['MI'] = mi_metric(vis, ir, fused).item()
    metrics['PSNR'] = psnr_metric(vis, ir, fused).item()
    metrics['Qabf'] = q_abf_metric(vis, ir, fused).item()
    metrics['SD'] = sd_metric(vis, ir, fused).item()
    metrics['SF'] = sf_metric(vis, ir, fused).item()
    metrics['SSIM'] = ssim_metric(vis, ir, fused).item()
    metrics['VIF'] = vif_metric(vis, ir, fused).item()
    metrics['SCD'] = scd_metric(vis, ir, fused).item()
    metrics['CC'] = cc_metric(vis, ir, fused).item()
    return metrics

losses = []
metrics_history = []  # 存储每次迭代的指标
milestone_metrics = []  # 存储每100轮的指标
milestone_iterations = []  # 存储里程碑迭代次数

def monotone_incresing_loss(x, y): # x为优化后的融合图， y为原始融合图
    # Calculate the difference between the optimized image and the original image for each metric
    ssim_diff = (ssim.ssim_metric(vis, ir, y) - ssim.ssim_metric(vis, ir, x)) / ssim.ssim_metric(vis, ir, x) 
    mi_diff = (mi.mi_metric(vis, ir, y) - mi.mi_metric(vis, ir, x)) / mi.mi_metric(vis, ir, x) 
    # rmse_diff = (rmse.rmse_metric(vis, ir, x) - rmse.rmse_metric(vis, ir, y)) / rmse.rmse_metric(vis, ir, x) 
    qabf_diff = (qabf.q_abf_metric(vis, ir, y) - qabf.q_abf_metric(vis, ir, x)) / qabf.q_abf_metric(vis, ir, x) 
    psnr_diff = (psnr.psnr_metric(vis, ir, y) - psnr.psnr_metric(vis, ir, x)) / psnr.psnr_metric(vis, ir, x) 
    sf_diff = (sf.sf_metric(vis, ir, y) - sf.sf_metric(vis, ir, x)) / sf.sf_metric(vis, ir, x) 
    # ce_diff = (ce.ce_metric(vis, ir, x) - ce.ce_metric(vis, ir, y)) / ce.ce_metric(vis, ir, x)
    # en_diff = (en.en_metric(vis, ir, y) - en.en_metric(vis, ir, x)) / en.en_metric(vis, ir, x)
    # ag_diff = (ag.ag_metric(vis, ir, y) - ag.ag_metric(vis, ir, x)) / ag.ag_metric(vis, ir, x)
    # ei_diff = (ei.ei_metric(vis, ir, y) - ei.ei_metric(vis, ir, x)) / ei.ei_metric(vis, ir, x)
    sd_diff = (sd.sd_metric(vis, ir, y) - sd.sd_metric(vis, ir, x)) / sd.sd_metric(vis, ir, x)
    # qcb_diff = (q_cb.q_cb_metric(vis, ir, y) - q_cb.q_cb(vis, ir, x)) / q_cb.q_cb_metric(vis, ir, x)
    vif_diff = (vif.vif_metric(vis, ir, y) - vif.vif(vis, ir, x)) / vif.vif_metric(vis, ir, x)
    scd_diff = (scd.scd_metric(vis, ir, y) - scd.scd(vis, ir, x)) / scd.scd_metric(vis, ir, x)
    cc_diff = (cc.cc_metric(vis, ir, y) - cc.cc(vis, ir, x)) / cc.cc_metric(vis, ir, x)

    # Apply the mapping function to the differences
    def mapping_function(diff):
        mapped_diff = (torch.exp(5.5 * diff) - torch.exp(5.5 * -diff))/ (torch.exp(5.5 * diff) + torch.exp(5.5 * -diff))
        return mapped_diff

    # Apply the mapping function to each difference
    ssim_mapped = mapping_function(ssim_diff)
    mi_mapped = mapping_function(mi_diff)
    # # rmse_mapped = mapping_function(rmse_diff)
    qabf_mapped = mapping_function(qabf_diff)
    psnr_mapped = mapping_function(psnr_diff)
    sf_mapped = mapping_function(sf_diff)
    # ce_mapped = mapping_function(ce_diff)
    # en_mapped = mapping_function(en_diff)
    # ag_mapped = mapping_function(ag_diff)
    # ei_mapped = mapping_function(ei_diff)
    sd_mapped = mapping_function(sd_diff)
    # qcb_mapped = mapping_function(qcb_diff)    
    vif_mapped = mapping_function(vif_diff)    
    scd_mapped = mapping_function(scd_diff)  
    cc_mapped = mapping_function(cc_diff)

    # print(ssim_mapped, mi_mapped, qabf_mapped, psnr_mapped, sf_mapped, sd_mapped, vif_mapped, scd_mapped, cc_mapped)

    # Calculate the loss
    # loss = ( 
    #     ssim_mapped  +
    #     mi_mapped  +  
    #     qabf_mapped + 
    #     psnr_mapped +
    #     sf_mapped  +
    #     sd_mapped +
    #     vif_mapped +
    #     scd_mapped +
    #     cc_mapped 

    #     ) / 9
    
    loss = ( 
        ssim_mapped +
        mi_mapped * 0.01 +  
        qabf_mapped * 0.5 + 
        # psnr_mapped +
        sf_mapped * 0.1 +
        # sd_mapped +
        vif_mapped +
        scd_mapped +
        cc_mapped  

        ) / 7
    
    return loss

# Define the optimizer
optimizer = optim.Adam([optimized_fused], lr)

os.makedirs(output_folder, exist_ok=True)

running_times = []  

# Training iterations
print("开始优化...")
for i in range(num_iterations + 1):

    start_time = time.time()
    # Calculate the new loss
    current_loss = monotone_incresing_loss(optimized_fused, fused)
    # Backpropagation and optimization
    optimizer.zero_grad()
    current_loss.backward()
    optimizer.step()

    losses.append(current_loss.item())

    running_time = time.time() - start_time
    running_times.append(running_time)

    # 计算当前迭代的指标（用于记录）
    vis_for_metrics = read_image_for_metrics(vis_path)
    ir_for_metrics = read_image_for_metrics(ir_path)
    current_metrics = calculate_metrics_absolute(vis_for_metrics, ir_for_metrics, optimized_fused)
    current_metrics['Iteration'] = i
    current_metrics['Loss'] = current_loss.item()
    metrics_history.append(current_metrics)

    # 每100轮保存图像和记录里程碑指标
    if i % 100 == 0 or i == num_iterations:
        # 保存当前优化图像
        milestone_image = optimized_fused.detach().clone().clamp(0, 1)
        milestone_image_path = f"{output_folder}{scene}_{method}_iter_{i}.png"
        TF.to_pil_image(milestone_image.squeeze(0).cpu()).save(milestone_image_path)

        # 记录里程碑指标
        milestone_metrics.append(current_metrics.copy())
        milestone_iterations.append(i)

        print(f"=== Milestone {i}/{num_iterations} ===")
        print(f"Loss: {current_loss.item():.6f}, Time: {running_time:.2f}s")
        print(f"SSIM: {current_metrics['SSIM']:.4f}, MI: {current_metrics['MI']:.4f}, Qabf: {current_metrics['Qabf']:.4f}")
        print(f"PSNR: {current_metrics['PSNR']:.4f}, VIF: {current_metrics['VIF']:.4f}, CC: {current_metrics['CC']:.4f}")
        print(f"图像已保存: {milestone_image_path}")
        print("-" * 50)

    # 每10次迭代显示一次简要进度
    elif i % 10 == 0:
        print(f"Iteration {i}/{num_iterations}, Loss: {current_loss.item():.6f}, Time: {running_time:.2f}s")

print("优化完成！")

# 保存最终优化后的图像
print("保存最终结果...")
final_optimized_image = optimized_fused.detach().clone().clamp(0, 1)
final_optimized_path = f"{output_folder}{scene}_{method}_opt.png"
TF.to_pil_image(final_optimized_image.squeeze(0).cpu()).save(final_optimized_path)
print(f"最终优化图像已保存到: {final_optimized_path}")

# 计算最终指标对比
vis_for_metrics = read_image_for_metrics(vis_path)
ir_for_metrics = read_image_for_metrics(ir_path)
fused_for_metrics = read_image_for_metrics(fused_path)

original_metrics = calculate_metrics_absolute(vis_for_metrics, ir_for_metrics, fused_for_metrics)
final_metrics = calculate_metrics_absolute(vis_for_metrics, ir_for_metrics, optimized_fused)

# 创建对比结果
comparison_data = {
    'Metric': list(original_metrics.keys()),
    'Original': list(original_metrics.values()),
    'Optimized': list(final_metrics.values())
}

comparison_df = pd.DataFrame(comparison_data)
comparison_excel_path = f"{output_folder}{scene}_{method}_metrics_comparison.xlsx"
comparison_df.to_excel(comparison_excel_path, index=False)
print(f"指标对比结果已保存到: {comparison_excel_path}")

# 保存完整的指标历史记录
if metrics_history:
    history_df = pd.DataFrame(metrics_history)
    history_excel_path = f"{output_folder}{scene}_{method}_metrics_history.xlsx"
    history_df.to_excel(history_excel_path, index=False)
    print(f"完整指标历史已保存到: {history_excel_path}")

# 保存每100轮的里程碑指标
if milestone_metrics:
    milestone_df = pd.DataFrame(milestone_metrics)
    milestone_excel_path = f"{output_folder}{scene}_{method}_milestone_metrics.xlsx"
    milestone_df.to_excel(milestone_excel_path, index=False)
    print(f"里程碑指标已保存到: {milestone_excel_path}")

# 绘制指标变化曲线
def plot_metrics_curves():
    if not milestone_metrics:
        print("没有里程碑数据，跳过指标曲线绘制")
        return

    # 提取指标数据
    metric_names = ['SSIM', 'MI', 'Qabf', 'PSNR', 'VIF', 'CC', 'EN', 'CE', 'SD', 'SF', 'SCD']

    # 创建子图
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle(f'Metrics Evolution - {method} {scene}', fontsize=16)

    # 展平axes数组以便索引
    axes_flat = axes.flatten()

    for idx, metric_name in enumerate(metric_names):
        if idx >= len(axes_flat):
            break

        ax = axes_flat[idx]

        # 提取该指标的数值
        metric_values = [m[metric_name] for m in milestone_metrics]

        # 绘制曲线
        ax.plot(milestone_iterations, metric_values, 'b-o', linewidth=2, markersize=4)
        ax.set_title(f'{metric_name}', fontsize=12)
        ax.set_xlabel('Iteration')
        ax.set_ylabel(metric_name)
        ax.grid(True, alpha=0.3)

        # 标注起始和结束值
        if len(metric_values) >= 2:
            ax.annotate(f'{metric_values[0]:.4f}',
                       xy=(milestone_iterations[0], metric_values[0]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)
            ax.annotate(f'{metric_values[-1]:.4f}',
                       xy=(milestone_iterations[-1], metric_values[-1]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)

    # 隐藏多余的子图
    for idx in range(len(metric_names), len(axes_flat)):
        axes_flat[idx].set_visible(False)

    plt.tight_layout()
    metrics_curve_path = os.path.join(output_folder, 'Metrics_Curves.png')
    plt.savefig(metrics_curve_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"指标变化曲线已保存到: {metrics_curve_path}")

# 绘制Loss曲线
plt.figure(figsize=(12, 6))
plt.plot(losses, 'r-', linewidth=1)
plt.title(f'Loss Curve - {method} {scene}', fontsize=14)
plt.xlabel('Iteration')
plt.ylabel('Loss')
plt.grid(True, alpha=0.3)

# 标注里程碑点
for i, iteration in enumerate(milestone_iterations):
    if iteration < len(losses):
        plt.plot(iteration, losses[iteration], 'bo', markersize=6)
        plt.annotate(f'Iter {iteration}',
                    xy=(iteration, losses[iteration]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)

plt.tight_layout()
loss_curve_path = os.path.join(output_folder, 'Loss_Curve.png')
plt.savefig(loss_curve_path, dpi=300, bbox_inches='tight')
plt.show()
print(f"Loss曲线已保存到: {loss_curve_path}")

# 调用绘制函数
plot_metrics_curves()

# 生成里程碑对比表格
def generate_milestone_comparison():
    if not milestone_metrics:
        return

    print("\n=== 里程碑指标对比 ===")
    metric_names = ['SSIM', 'MI', 'Qabf', 'PSNR', 'VIF', 'CC', 'EN', 'CE', 'SD', 'SF', 'SCD']

    # 打印表头
    header = f"{'Iteration':<10}"
    for metric in metric_names:
        header += f"{metric:<8}"
    print(header)
    print("-" * len(header))

    # 打印每个里程碑的数据
    for i, metrics in enumerate(milestone_metrics):
        row = f"{milestone_iterations[i]:<10}"
        for metric in metric_names:
            row += f"{metrics[metric]:<8.4f}"
        print(row)

    # 保存里程碑对比到Excel
    milestone_comparison_data = {'Iteration': milestone_iterations}
    for metric in metric_names:
        milestone_comparison_data[metric] = [m[metric] for m in milestone_metrics]

    milestone_comparison_df = pd.DataFrame(milestone_comparison_data)
    milestone_comparison_path = f"{output_folder}{scene}_{method}_milestone_comparison.xlsx"
    milestone_comparison_df.to_excel(milestone_comparison_path, index=False)
    print(f"\n里程碑对比表已保存到: {milestone_comparison_path}")

# 调用里程碑对比函数
generate_milestone_comparison()

# 显示最终结果对比
print("\n=== 最终结果对比 ===")
print(f"{'Metric':<8} {'Original':<12} {'Optimized':<12} {'Difference':<12} {'Improvement':<12}")
print("-" * 70)
for metric_name in original_metrics.keys():
    orig_val = original_metrics[metric_name]
    opt_val = final_metrics[metric_name]
    diff = opt_val - orig_val

    # 计算改善百分比
    if metric_name == 'CE':  # CE越小越好
        improvement = -((opt_val - orig_val) / orig_val) * 100 if orig_val != 0 else 0
    else:  # 其他指标越大越好
        improvement = ((opt_val - orig_val) / orig_val) * 100 if orig_val != 0 else 0

    print(f"{metric_name:<8} {orig_val:<12.4f} {opt_val:<12.4f} {diff:<12.4f} {improvement:<12.2f}%")

# 统计信息
average_running_time = sum(running_times) / len(running_times)
total_running_time = sum(running_times)
print(f"\n=== 运行统计 ===")
print(f"Method: {method}, Scene: {scene}")
print(f"Total iterations: {num_iterations}")
print(f"Milestone saves: {len(milestone_iterations)} (every 100 iterations)")
print(f"Average running time per iteration: {average_running_time:.2f} seconds")
print(f"Total running time: {total_running_time:.2f} seconds")
print(f"Final loss: {losses[-1]:.6f}")

# 保存运行统计
stats_data = {
    'Parameter': ['Method', 'Scene', 'Total_Iterations', 'Milestone_Saves', 'Avg_Time_Per_Iter', 'Total_Time', 'Final_Loss'],
    'Value': [method, scene, num_iterations, len(milestone_iterations), f"{average_running_time:.2f}s", f"{total_running_time:.2f}s", f"{losses[-1]:.6f}"]
}
stats_df = pd.DataFrame(stats_data)
stats_path = f"{output_folder}{scene}_{method}_statistics.xlsx"
stats_df.to_excel(stats_path, index=False)
print(f"运行统计已保存到: {stats_path}")
